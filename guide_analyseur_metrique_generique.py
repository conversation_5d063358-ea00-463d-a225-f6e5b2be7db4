# -*- coding: utf-8 -*-
"""
GUIDE D'UTILISATION : ANALYSEUR MÉTRIQUE GÉNÉRIQUE
==================================================

Ce fichier documente comment utiliser la classe AnalyseurMetriqueGenerique
pour reproduire l'architecture DIFF avec toutes les métriques d'entropie
disponibles dans formules/formules_entropie_python.txt

ARCHITECTURE REPRODUITE DEPUIS DIFF :
=====================================
✅ Tranches de qualité personnalisables (9 niveaux)
✅ Combinaisons avec ratios L4/L5 (16+ conditions)
✅ Analyse exhaustive des conditions S/O
✅ Génération de rapports spécialisés
✅ Calculs de corrélations essentielles
✅ Formules mathématiques dérivées
✅ Affichage des résultats synthétiques

MÉTRIQUES SUPPORTÉES :
=====================
Basées sur formules/formules_entropie_python.txt (2406 lignes, 184 formules)

1. ENTROPIES FONDAMENTALES
   - Entropie de Shannon
   - Entropie de Bernoulli  
   - Entropie uniforme

2. ENTROPIES RELATIVES ET DIVERGENCES
   - Divergence de Kullback-Leibler
   - Entropie relative pour Bernoulli

3. INFORMATION MUTUELLE ET ENTROPIES JOINTES
   - Information mutuelle
   - Entropie jointe
   - Information mutuelle conditionnelle

4. ENTROPIES CONDITIONNELLES
   - Entropie conditionnelle
   - Entropie conditionnelle pour chaînes

5. ENTROPIE CROISÉE ET CODAGE
   - Entropie croisée
   - Métriques de codage de source

6. CHAÎNES DE MARKOV ET PROCESSUS
   - Entropie des chaînes de Markov
   - Entropie métrique (systèmes dynamiques)

7. ÉQUIPARTITION ASYMPTOTIQUE
   - Théorème d'équipartition
   - Shannon-McMillan-Breiman

8. THÉORIE DES CANAUX
   - Capacité d'un canal
   - Métriques de transmission

UTILISATION PRATIQUE :
=====================
"""

from analyse_complete_avec_diff import AnalyseurMetriqueGenerique
import math

# ============================================================================
# EXEMPLE 1: ENTROPIE DE SHANNON
# ============================================================================

def exemple_shannon_entropy():
    """Exemple complet avec l'entropie de Shannon"""
    
    # Fonction de calcul
    def calculer_shannon(donnee):
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)
        
        total = ratio_l4 + ratio_l5
        if total == 0:
            return 0.0
        
        p1, p2 = ratio_l4/total, ratio_l5/total
        entropy = 0.0
        for p in [p1, p2]:
            if p > 0:
                entropy -= p * math.log2(p)
        return entropy
    
    # Tranches spécialisées (0 à 1 bit pour Shannon binaire)
    tranches = [
        (0.0, 0.1, "SIGNAL_PARFAIT"),
        (0.1, 0.2, "SIGNAL_EXCELLENT"), 
        (0.2, 0.4, "SIGNAL_TRÈS_BON"),
        (0.4, 0.6, "SIGNAL_BON"),
        (0.6, 0.8, "SIGNAL_ACCEPTABLE"),
        (0.8, 0.9, "SIGNAL_RISQUÉ"),
        (0.9, 0.95, "SIGNAL_DOUTEUX"),
        (0.95, 0.99, "SIGNAL_TRÈS_DOUTEUX"),
        (0.99, 10.0, "SIGNAL_INUTILISABLE")
    ]
    
    # Formules dérivées
    formules = {
        "Entropie_Maximum": "H_max = log₂(n) pour n symboles équiprobables",
        "Efficacité": "η = H(X) / H_max", 
        "Redondance": "R = 1 - η = 1 - H(X)/H_max",
        "Capacité_Information": "C = H_max - H(X)"
    }
    
    # Créer l'analyseur
    analyseur = AnalyseurMetriqueGenerique(
        nom_metrique="SHANNON_ENTROPY",
        fonction_calcul=calculer_shannon,
        tranches_qualite=tranches,
        formules_derivees=formules
    )
    
    return analyseur

# ============================================================================
# EXEMPLE 2: ENTROPIE DE BERNOULLI
# ============================================================================

def exemple_bernoulli_entropy():
    """Exemple avec l'entropie de Bernoulli h(a) = -a log₂(a) - (1-a) log₂(1-a)"""
    
    def calculer_bernoulli(donnee):
        a = donnee.get('ratio_l4', 0.5)
        a = max(0.001, min(0.999, a))  # Éviter les cas limites
        return -a * math.log2(a) - (1 - a) * math.log2(1 - a)
    
    # Tranches pour Bernoulli (maximum = 1 bit en a=0.5)
    tranches = [
        (0.0, 0.1, "DÉTERMINISTE_FORT"),
        (0.1, 0.3, "DÉTERMINISTE_MODÉRÉ"),
        (0.3, 0.5, "DÉTERMINISTE_FAIBLE"),
        (0.5, 0.7, "INCERTAIN_FAIBLE"),
        (0.7, 0.9, "INCERTAIN_MODÉRÉ"),
        (0.9, 0.95, "INCERTAIN_FORT"),
        (0.95, 0.99, "MAXIMUM_INCERTITUDE"),
        (0.99, 1.0, "QUASI_MAXIMUM"),
        (1.0, 10.0, "IMPOSSIBLE")
    ]
    
    formules = {
        "Maximum_Bernoulli": "h(0.5) = 1 bit (maximum d'incertitude)",
        "Propriété_Symétrie": "h(a) = h(1-a)",
        "Cas_Déterministes": "h(0) = h(1) = 0",
        "Dérivée": "h'(a) = log₂((1-a)/a)"
    }
    
    return AnalyseurMetriqueGenerique(
        nom_metrique="BERNOULLI_ENTROPY",
        fonction_calcul=calculer_bernoulli,
        tranches_qualite=tranches,
        formules_derivees=formules
    )

# ============================================================================
# EXEMPLE 3: INFORMATION MUTUELLE
# ============================================================================

def exemple_mutual_information():
    """Exemple avec l'information mutuelle I(X;Y)"""
    
    def calculer_mutual_info(donnee):
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)
        
        # Approximation basée sur la corrélation
        correlation = 1.0 - abs(ratio_l4 - ratio_l5)
        
        # Transformer en information mutuelle (0 = indépendant, 1 = dépendant)
        return max(0.0, correlation)
    
    tranches = [
        (0.0, 0.1, "INDÉPENDANCE_TOTALE"),
        (0.1, 0.2, "INDÉPENDANCE_FORTE"),
        (0.2, 0.4, "INDÉPENDANCE_MODÉRÉE"),
        (0.4, 0.6, "DÉPENDANCE_FAIBLE"),
        (0.6, 0.8, "DÉPENDANCE_MODÉRÉE"),
        (0.8, 0.9, "DÉPENDANCE_FORTE"),
        (0.9, 0.95, "DÉPENDANCE_TRÈS_FORTE"),
        (0.95, 0.99, "QUASI_DÉTERMINISME"),
        (0.99, 10.0, "DÉTERMINISME_TOTAL")
    ]
    
    formules = {
        "Symétrie": "I(X;Y) = I(Y;X)",
        "Borne_Supérieure": "I(X;Y) ≤ min(H(X), H(Y))",
        "Indépendance": "I(X;Y) = 0 ssi X et Y indépendantes",
        "Formule_Alternative": "I(X;Y) = H(X) + H(Y) - H(X,Y)"
    }
    
    return AnalyseurMetriqueGenerique(
        nom_metrique="MUTUAL_INFORMATION",
        fonction_calcul=calculer_mutual_info,
        tranches_qualite=tranches,
        formules_derivees=formules
    )

# ============================================================================
# EXEMPLE 4: DIVERGENCE DE KULLBACK-LEIBLER
# ============================================================================

def exemple_kl_divergence():
    """Exemple avec la divergence KL D(p||q)"""
    
    def calculer_kl_divergence(donnee):
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)
        
        # Créer deux distributions
        p = [ratio_l4, 1 - ratio_l4]
        q = [ratio_l5, 1 - ratio_l5]
        
        divergence = 0.0
        for i in range(len(p)):
            if p[i] > 0:
                if q[i] > 0:
                    divergence += p[i] * math.log2(p[i] / q[i])
                else:
                    return 10.0  # Valeur élevée pour divergence infinie
        
        return max(0.0, divergence)
    
    tranches = [
        (0.0, 0.01, "DISTRIBUTIONS_IDENTIQUES"),
        (0.01, 0.05, "TRÈS_SIMILAIRES"),
        (0.05, 0.1, "SIMILAIRES"),
        (0.1, 0.2, "MODÉRÉMENT_DIFFÉRENTES"),
        (0.2, 0.5, "DIFFÉRENTES"),
        (0.5, 1.0, "TRÈS_DIFFÉRENTES"),
        (1.0, 2.0, "EXTRÊMEMENT_DIFFÉRENTES"),
        (2.0, 5.0, "QUASI_ORTHOGONALES"),
        (5.0, 100.0, "ORTHOGONALES")
    ]
    
    formules = {
        "Non_Négativité": "D(p||q) ≥ 0, égalité ssi p = q",
        "Non_Symétrie": "D(p||q) ≠ D(q||p) en général",
        "Relation_Entropie_Croisée": "D(p||q) = H(p,q) - H(p)",
        "Inégalité_Gibbs": "D(p||q) ≥ 0 (théorème fondamental)"
    }
    
    return AnalyseurMetriqueGenerique(
        nom_metrique="KL_DIVERGENCE",
        fonction_calcul=calculer_kl_divergence,
        tranches_qualite=tranches,
        formules_derivees=formules
    )

# ============================================================================
# EXEMPLE 5: ENTROPIE CONDITIONNELLE
# ============================================================================

def exemple_conditional_entropy():
    """Exemple avec l'entropie conditionnelle H(Y|X)"""
    
    def calculer_conditional_entropy(donnee):
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)
        
        # Approximation : corrélation réduit l'entropie conditionnelle
        correlation_strength = 1.0 - abs(ratio_l4 - ratio_l5)
        
        # Entropie de base de L5
        if ratio_l5 <= 0.001 or ratio_l5 >= 0.999:
            base_entropy = 0.0
        else:
            base_entropy = -ratio_l5 * math.log2(ratio_l5) - (1 - ratio_l5) * math.log2(1 - ratio_l5)
        
        # Réduire selon la corrélation
        return base_entropy * (1.0 - correlation_strength)
    
    tranches = [
        (0.0, 0.05, "DÉTERMINISME_TOTAL"),
        (0.05, 0.1, "QUASI_DÉTERMINISME"),
        (0.1, 0.2, "PRÉDICTIBILITÉ_FORTE"),
        (0.2, 0.4, "PRÉDICTIBILITÉ_MODÉRÉE"),
        (0.4, 0.6, "PRÉDICTIBILITÉ_FAIBLE"),
        (0.6, 0.8, "INCERTITUDE_MODÉRÉE"),
        (0.8, 0.9, "INCERTITUDE_FORTE"),
        (0.9, 0.95, "QUASI_INDÉPENDANCE"),
        (0.95, 10.0, "INDÉPENDANCE_TOTALE")
    ]
    
    formules = {
        "Borne_Supérieure": "H(Y|X) ≤ H(Y), égalité ssi X et Y indépendantes",
        "Borne_Inférieure": "H(Y|X) ≥ 0, égalité ssi Y déterminé par X",
        "Relation_Information_Mutuelle": "I(X;Y) = H(Y) - H(Y|X)",
        "Règle_Chaîne": "H(X,Y) = H(X) + H(Y|X)"
    }
    
    return AnalyseurMetriqueGenerique(
        nom_metrique="CONDITIONAL_ENTROPY",
        fonction_calcul=calculer_conditional_entropy,
        tranches_qualite=tranches,
        formules_derivees=formules
    )

# ============================================================================
# FONCTION PRINCIPALE DE DÉMONSTRATION
# ============================================================================

def demonstration_complete():
    """
    Démonstration complète de l'analyseur générique avec 5 métriques différentes
    """
    print("🚀 DÉMONSTRATION ANALYSEUR MÉTRIQUE GÉNÉRIQUE")
    print("=" * 60)
    print("Architecture DIFF reproduite pour 5 métriques d'entropie")
    print("=" * 60)
    
    # Liste des analyseurs à tester
    analyseurs = [
        ("Shannon Entropy", exemple_shannon_entropy()),
        ("Bernoulli Entropy", exemple_bernoulli_entropy()),
        ("Mutual Information", exemple_mutual_information()),
        ("KL Divergence", exemple_kl_divergence()),
        ("Conditional Entropy", exemple_conditional_entropy())
    ]
    
    # Données de test communes
    donnees_test = []
    for i in range(500):  # Dataset réduit pour la démo
        donnees_test.append({
            'partie_id': f'demo_{i}',
            'main': i % 50,
            'ratio_l4': 0.2 + (i % 8) * 0.1,  # 0.2 à 0.9
            'ratio_l5': 0.1 + (i % 9) * 0.1,  # 0.1 à 0.9
            'diff_l4': (i % 4) * 0.025,
            'diff_l5': (i % 5) * 0.02,
            'pattern': 'S' if i % 2 == 0 else 'O',
            'index3': i % 8
        })
    
    resultats = []
    
    for nom_metrique, analyseur in analyseurs:
        print(f"\n📊 ANALYSE: {nom_metrique}")
        print("-" * 40)
        
        try:
            # Enrichir les données
            donnees_enrichies = analyseur.calculer_metrique_sur_donnees(donnees_test)
            
            # Analyser les conditions
            conditions_s, conditions_o = analyseur.analyser_toutes_conditions_avec_metrique(donnees_enrichies)
            
            # Calculer les corrélations
            correlations_stats = analyseur.calculer_correlations_essentielles_metrique(donnees_enrichies)
            
            # Générer le rapport
            nom_rapport = analyseur.generer_tableau_predictif_avec_metrique(len(donnees_enrichies))
            
            # Afficher les résultats
            analyseur.afficher_resultats_avec_metrique()
            
            resultats.append({
                'metrique': nom_metrique,
                'conditions_s': len(conditions_s),
                'conditions_o': len(conditions_o),
                'rapport': nom_rapport,
                'succes': True
            })
            
            print(f"✅ {nom_metrique}: SUCCÈS")
            
        except Exception as e:
            print(f"❌ {nom_metrique}: ÉCHEC - {e}")
            resultats.append({
                'metrique': nom_metrique,
                'succes': False,
                'erreur': str(e)
            })
    
    # Résumé final
    print(f"\n🎯 RÉSUMÉ DÉMONSTRATION")
    print("=" * 40)
    
    succes_total = sum(1 for r in resultats if r.get('succes', False))
    
    for resultat in resultats:
        if resultat.get('succes', False):
            print(f"✅ {resultat['metrique']}: {resultat['conditions_s']} cond. S, {resultat['conditions_o']} cond. O")
        else:
            print(f"❌ {resultat['metrique']}: {resultat.get('erreur', 'Erreur inconnue')}")
    
    print(f"\n📊 BILAN: {succes_total}/{len(analyseurs)} métriques analysées avec succès")
    print("🚀 Architecture DIFF reproduite et opérationnelle pour toutes les métriques d'entropie")
    
    return succes_total == len(analyseurs)

if __name__ == "__main__":
    demonstration_complete()
