# -*- coding: utf-8 -*-
"""
DÉMONSTRATION COMPLÈTE : TOUTES LES MÉTRIQUES D'ENTROPIE
========================================================

Ce script démontre l'utilisation de l'AnalyseurMetriqueGenerique
avec toutes les métriques disponibles dans le catalogue.

OBJECTIF :
- Reproduire l'architecture DIFF pour chaque métrique d'entropie
- Générer des rapports complets pour chaque métrique
- Comparer les performances prédictives entre métriques
- Identifier les meilleures métriques pour la prédiction S/O

MÉTRIQUES TESTÉES :
- Shannon Entropy
- Bernoulli Entropy  
- Mutual Information
- KL Divergence
- Conditional Entropy
- Cross Entropy
- Joint Entropy
- Et toutes les autres du catalogue...

UTILISATION :
python demo_toutes_metriques.py
"""

from catalogue_metriques_entropie import *
from analyse_complete_avec_diff import AnalyseurMetriqueGenerique
import time
import os

def generer_donnees_test_realistes(nb_points=2000):
    """
    Génère des données de test plus réalistes pour la démonstration
    
    Args:
        nb_points (int): Nombre de points de données à générer
        
    Returns:
        list: Données de test avec patterns réalistes
    """
    import random
    random.seed(42)  # Pour la reproductibilité
    
    donnees = []
    
    for i in range(nb_points):
        # Générer des ratios avec des patterns réalistes
        base_l4 = 0.2 + (i % 10) * 0.08  # 0.2 à 0.92
        base_l5 = 0.15 + (i % 12) * 0.07  # 0.15 à 0.92
        
        # Ajouter du bruit réaliste
        noise_l4 = random.uniform(-0.05, 0.05)
        noise_l5 = random.uniform(-0.05, 0.05)
        
        ratio_l4 = max(0.1, min(0.95, base_l4 + noise_l4))
        ratio_l5 = max(0.1, min(0.95, base_l5 + noise_l5))
        
        # Patterns S/O avec logique réaliste
        # S plus probable quand les ratios sont stables
        diff_ratios = abs(ratio_l4 - ratio_l5)
        prob_s = 0.3 + 0.4 * (1.0 - diff_ratios)  # 30% à 70%
        
        pattern = 'S' if random.random() < prob_s else 'O'
        
        donnees.append({
            'partie_id': f'demo_{i}',
            'main': i % 80,
            'ratio_l4': ratio_l4,
            'ratio_l5': ratio_l5,
            'diff_l4': random.uniform(0, 0.08),
            'diff_l5': random.uniform(0, 0.1),
            'pattern': pattern,
            'index3': i % 12
        })
    
    return donnees

def analyser_metrique_complete(nom_metrique, config_metrique, donnees_test):
    """
    Analyse complète d'une métrique avec l'architecture DIFF
    
    Args:
        nom_metrique (str): Nom de la métrique
        config_metrique (dict): Configuration de la métrique
        donnees_test (list): Données de test
        
    Returns:
        dict: Résultats de l'analyse
    """
    print(f"\n🔬 ANALYSE MÉTRIQUE: {nom_metrique}")
    print("-" * 60)
    
    try:
        # Créer l'analyseur
        analyseur = AnalyseurMetriqueGenerique(
            nom_metrique=nom_metrique,
            fonction_calcul=config_metrique["fonction"],
            tranches_qualite=config_metrique["tranches"],
            formules_derivees=config_metrique["formules_derivees"]
        )
        
        # Enrichir les données
        start_time = time.time()
        donnees_enrichies = analyseur.calculer_metrique_sur_donnees(donnees_test)
        enrichissement_time = time.time() - start_time
        
        # Analyser les conditions
        start_time = time.time()
        conditions_s, conditions_o = analyseur.analyser_toutes_conditions_avec_metrique(donnees_enrichies)
        analyse_time = time.time() - start_time
        
        # Calculer les corrélations
        start_time = time.time()
        correlations_stats = analyseur.calculer_correlations_essentielles_metrique(donnees_enrichies)
        correlation_time = time.time() - start_time
        
        # Générer le rapport
        start_time = time.time()
        nom_rapport = analyseur.generer_tableau_predictif_avec_metrique(len(donnees_enrichies))
        rapport_time = time.time() - start_time
        
        # Afficher les résultats
        analyseur.afficher_resultats_avec_metrique()
        
        # Calculer les métriques de performance
        total_conditions = len(conditions_s) + len(conditions_o)
        conditions_metrique_s = [c for c in conditions_s if nom_metrique in c['nom']]
        conditions_metrique_o = [c for c in conditions_o if nom_metrique in c['nom']]
        
        meilleur_s = max(conditions_metrique_s, key=lambda x: x['pourcentage_s']) if conditions_metrique_s else None
        meilleur_o = max(conditions_metrique_o, key=lambda x: x['pourcentage_o']) if conditions_metrique_o else None
        
        # Corrélation la plus forte
        correlations = correlations_stats.get('correlations', {})
        max_correlation = max(correlations.items(), key=lambda x: abs(x[1])) if correlations else (None, 0)
        
        resultats = {
            'metrique': nom_metrique,
            'succes': True,
            'conditions_s': len(conditions_s),
            'conditions_o': len(conditions_o),
            'conditions_metrique_s': len(conditions_metrique_s),
            'conditions_metrique_o': len(conditions_metrique_o),
            'total_conditions': total_conditions,
            'meilleur_s': meilleur_s,
            'meilleur_o': meilleur_o,
            'max_correlation': max_correlation,
            'rapport': nom_rapport,
            'temps': {
                'enrichissement': enrichissement_time,
                'analyse': analyse_time,
                'correlations': correlation_time,
                'rapport': rapport_time,
                'total': enrichissement_time + analyse_time + correlation_time + rapport_time
            },
            'formule': config_metrique["formule"],
            'description': config_metrique["description"]
        }
        
        print(f"✅ {nom_metrique}: SUCCÈS")
        print(f"   📊 Conditions totales: {total_conditions}")
        print(f"   ⏱️ Temps total: {resultats['temps']['total']:.3f}s")
        
        return resultats
        
    except Exception as e:
        print(f"❌ {nom_metrique}: ÉCHEC - {e}")
        return {
            'metrique': nom_metrique,
            'succes': False,
            'erreur': str(e),
            'formule': config_metrique.get("formule", "N/A"),
            'description': config_metrique.get("description", "N/A")
        }

def demonstration_complete_toutes_metriques():
    """
    Démonstration complète avec toutes les métriques du catalogue
    """
    print("🚀 DÉMONSTRATION COMPLÈTE : TOUTES LES MÉTRIQUES D'ENTROPIE")
    print("=" * 80)
    print("Architecture DIFF reproduite pour chaque métrique disponible")
    print("=" * 80)
    
    # Générer des données de test réalistes
    print("\n📊 GÉNÉRATION DONNÉES DE TEST RÉALISTES")
    print("-" * 50)
    donnees_test = generer_donnees_test_realistes(2000)
    print(f"✅ {len(donnees_test)} points de données générés")
    
    # Analyser la distribution S/O
    count_s = sum(1 for d in donnees_test if d['pattern'] == 'S')
    count_o = len(donnees_test) - count_s
    print(f"📊 Distribution: {count_s} S ({count_s/len(donnees_test)*100:.1f}%), {count_o} O ({count_o/len(donnees_test)*100:.1f}%)")
    
    # Récupérer toutes les métriques
    toutes_metriques = lister_toutes_metriques()
    print(f"\n🔬 ANALYSE DE {len(toutes_metriques)} MÉTRIQUES")
    print("-" * 50)
    
    resultats_globaux = []
    temps_debut = time.time()
    
    # Analyser chaque métrique
    for nom_metrique, config in toutes_metriques.items():
        resultat = analyser_metrique_complete(nom_metrique, config, donnees_test)
        resultats_globaux.append(resultat)
    
    temps_total = time.time() - temps_debut
    
    # Générer le rapport de synthèse
    print(f"\n📊 RAPPORT DE SYNTHÈSE GLOBAL")
    print("=" * 60)
    
    metriques_reussies = [r for r in resultats_globaux if r.get('succes', False)]
    metriques_echouees = [r for r in resultats_globaux if not r.get('succes', False)]
    
    print(f"✅ Métriques analysées avec succès: {len(metriques_reussies)}/{len(toutes_metriques)}")
    print(f"❌ Métriques échouées: {len(metriques_echouees)}")
    print(f"⏱️ Temps total d'analyse: {temps_total:.2f}s")
    print(f"⏱️ Temps moyen par métrique: {temps_total/len(toutes_metriques):.3f}s")
    
    if metriques_reussies:
        print(f"\n🏆 TOP 5 MÉTRIQUES PAR NOMBRE DE CONDITIONS")
        print("-" * 50)
        
        # Trier par nombre total de conditions
        top_conditions = sorted(metriques_reussies, key=lambda x: x['total_conditions'], reverse=True)[:5]
        
        for i, resultat in enumerate(top_conditions, 1):
            print(f"{i}. {resultat['metrique']}: {resultat['total_conditions']} conditions")
            print(f"   S: {resultat['conditions_s']}, O: {resultat['conditions_o']}")
            if resultat['meilleur_s']:
                print(f"   Meilleur S: {resultat['meilleur_s']['pourcentage_s']:.1f}%")
            if resultat['meilleur_o']:
                print(f"   Meilleur O: {resultat['meilleur_o']['pourcentage_o']:.1f}%")
            print()
        
        print(f"\n🎯 TOP 5 MÉTRIQUES PAR PERFORMANCE PRÉDICTIVE")
        print("-" * 50)
        
        # Trier par meilleure performance prédictive
        def score_predictif(r):
            score_s = r['meilleur_s']['pourcentage_s'] if r['meilleur_s'] else 50.0
            score_o = r['meilleur_o']['pourcentage_o'] if r['meilleur_o'] else 50.0
            return max(score_s, score_o)
        
        top_predictif = sorted(metriques_reussies, key=score_predictif, reverse=True)[:5]
        
        for i, resultat in enumerate(top_predictif, 1):
            score = score_predictif(resultat)
            print(f"{i}. {resultat['metrique']}: {score:.1f}% (meilleure prédiction)")
            print(f"   Formule: {resultat['formule']}")
            print(f"   Description: {resultat['description']}")
            print()
        
        print(f"\n🔗 TOP 5 MÉTRIQUES PAR CORRÉLATION")
        print("-" * 50)
        
        # Trier par corrélation la plus forte
        top_correlation = sorted(metriques_reussies, 
                               key=lambda x: abs(x['max_correlation'][1]) if x['max_correlation'][1] else 0, 
                               reverse=True)[:5]
        
        for i, resultat in enumerate(top_correlation, 1):
            corr_nom, corr_val = resultat['max_correlation']
            print(f"{i}. {resultat['metrique']}: {corr_val:.4f}")
            print(f"   Corrélation: {corr_nom}")
            print()
    
    if metriques_echouees:
        print(f"\n❌ MÉTRIQUES ÉCHOUÉES")
        print("-" * 30)
        for resultat in metriques_echouees:
            print(f"• {resultat['metrique']}: {resultat.get('erreur', 'Erreur inconnue')}")
    
    # Lister tous les rapports générés
    print(f"\n📄 RAPPORTS GÉNÉRÉS")
    print("-" * 30)
    for resultat in metriques_reussies:
        if 'rapport' in resultat:
            taille_fichier = os.path.getsize(resultat['rapport']) if os.path.exists(resultat['rapport']) else 0
            print(f"• {resultat['rapport']} ({taille_fichier:,} bytes)")
    
    print(f"\n🎯 CONCLUSION")
    print("-" * 20)
    print(f"✅ Architecture DIFF reproduite avec succès pour {len(metriques_reussies)} métriques")
    print(f"📊 {len(metriques_reussies)} rapports détaillés générés")
    print(f"🚀 Système générique opérationnel pour toutes les métriques d'entropie")
    print(f"🔬 Prêt pour l'analyse avec les 184+ formules du fichier formules_entropie_python.txt")
    
    return len(metriques_reussies) == len(toutes_metriques)

if __name__ == "__main__":
    succes_complet = demonstration_complete_toutes_metriques()
    
    if succes_complet:
        print(f"\n🏆 DÉMONSTRATION COMPLÈTE RÉUSSIE !")
        print("🚀 Toutes les métriques analysées avec succès")
    else:
        print(f"\n⚠️ DÉMONSTRATION PARTIELLEMENT RÉUSSIE")
        print("🔧 Certaines métriques nécessitent des ajustements")
