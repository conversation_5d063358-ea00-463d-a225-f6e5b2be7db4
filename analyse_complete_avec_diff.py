#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSE COMPLÈTE DES CONDITIONS PRÉDICTIVES S/O AVEC DIFF
=========================================================

SOMMAIRE DU PROGRAMME :
======================

I. CONFIGURATION ET IMPORTS (lignes 50-60)
   - Imports système et modules d'analyse
   - Configuration des chemins et paramètres

II. FONCTION PRINCIPALE D'ANALYSE (lignes 61-156)
   - analyser_conditions_predictives_so_avec_diff()
   - Orchestration complète du processus d'analyse
   - Gestion des 5 phases d'analyse

III. PHASE 1 : CHARGEMENT DES DONNÉES (lignes 75-127)
   - Chargement analyseur entropique
   - Chargement analyseur ratios
   - Vérification et initialisation des analyses

IV. PHASE 2 : EXTRACTION DES DONNÉES AVEC DIFF (lignes 128-156)
   - Extraction des données de toutes les parties
   - Calcul de la variable DIFF = |L4-L5|
   - Alignement des patterns avec les ratios

V. MOTEUR D'ANALYSE EXHAUSTIVE (lignes 157-245)
   - analyser_toutes_conditions_avec_diff()
   - Analyse DIFF par tranches de qualité
   - Analyse ratios L4 et L5 par tranches
   - Analyse combinaisons DIFF + Ratios

VI. ANALYSEUR DE TRANCHES (lignes 246-282)
   - analyser_tranche()
   - Calcul des pourcentages S/O
   - Classification des conditions prédictives

VII. GÉNÉRATEUR DE RAPPORT (lignes 283-352)
   - generer_tableau_predictif_avec_diff()
   - Création du tableau prédictif complet
   - Analyse spéciale des conditions DIFF

VIII. AFFICHAGE DES RÉSULTATS (lignes 353-376)
   - afficher_resultats_avec_diff()
   - Synthèse des conditions identifiées
   - Meilleures conditions DIFF

IX. POINT D'ENTRÉE PRINCIPAL (lignes 377-394)
   - Lancement et coordination générale
   - Gestion des succès et échecs

DESCRIPTION FONCTIONNELLE :
==========================

Ce programme analyse 100,000 parties de baccarat pour identifier les conditions
prédictives optimales pour les patterns S (continuation) et O (alternance).

INNOVATION MAJEURE : VARIABLE DIFF
- DIFF = |L4-L5| = Indicateur de cohérence entre ratios L4 et L5
- Signal de qualité pour la fiabilité des prédictions
- Classification par tranches de confiance

TRANCHES DE QUALITÉ DIFF :
- DIFF < 0.020 : Signal PARFAIT (confiance 95%)
- DIFF < 0.030 : Signal EXCELLENT (confiance 90%)
- DIFF < 0.050 : Signal TRÈS BON (confiance 85%)
- DIFF > 0.150 : Signal DOUTEUX (abstention recommandée)

ANALYSES RÉALISÉES :
1. Analyse DIFF pure (9 tranches de qualité)
2. Analyse ratios L4 (7 tranches d'ordre/chaos)
3. Analyse ratios L5 (7 tranches d'ordre/chaos)
4. Analyse combinaisons DIFF + Ratios (16 combinaisons critiques)

SORTIE :
- Tableau prédictif exhaustif avec toutes les conditions
- Classification par force (FORTE/MODÉRÉE/FAIBLE)
- Analyse spéciale des conditions DIFF

Auteur: Expert Statisticien
Date: 2025-06-23 - Version STRUCTURÉE ET DOCUMENTÉE
"""

# ============================================================================
# I. CONFIGURATION ET IMPORTS
# ============================================================================

import sys
import os
from datetime import datetime

# Ajouter le répertoire courant au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# ============================================================================
# II. FONCTION PRINCIPALE D'ANALYSE
# ============================================================================

def analyser_conditions_predictives_so_avec_diff():
    """
    FONCTION PRINCIPALE D'ANALYSE
    =============================

    Orchestre l'analyse complète des conditions prédictives pour S et O AVEC DIFF.
    Coordonne les 5 phases d'analyse et génère le rapport final.

    Returns:
        bool: True si l'analyse réussit, False sinon
    """
    print("🔬 ANALYSE COMPLÈTE CONDITIONS PRÉDICTIVES S/O AVEC DIFF")
    print("📊 CORRECTION MAJEURE : INCLUSION VARIABLE DIFF")
    print("🎯 DIFF = |L4-L5| = Indicateur qualité signal")
    print("=" * 70)
    
    try:
        # Import des modules d'analyse
        from analyseur_transitions_index5 import AnalyseurEvolutionEntropique, AnalyseurEvolutionRatios
        
        print("✅ Import des modules réussi")
        
        # ====================================================================
        # III. PHASE 1 : CHARGEMENT DES DONNÉES
        # ====================================================================
        print(f"\n📊 PHASE 1: CHARGEMENT DONNÉES 100,000 PARTIES")
        print("-" * 50)
        
        dataset_path = "dataset_baccarat_lupasco_20250626_044753.json"
        if not os.path.exists(dataset_path):
            print(f"❌ Dataset non trouvé: {dataset_path}")
            return False
        
        print("🔄 Chargement analyseur entropique...")
        analyseur_entropique = AnalyseurEvolutionEntropique(dataset_path)
        
        # Vérifier si l'analyse est déjà faite
        if not hasattr(analyseur_entropique, 'evolutions_entropiques') or not analyseur_entropique.evolutions_entropiques:
            print("🔄 Analyse entropique en cours...")
            resultats_entropiques = analyseur_entropique.analyser_toutes_parties_entropiques(nb_parties_max=100000)
            print(f"✅ {resultats_entropiques['parties_reussies']:,} parties analysées")
        else:
            print(f"✅ Données entropiques déjà disponibles")
        
        print("🔄 Chargement analyseur ratios...")
        analyseur_ratios = AnalyseurEvolutionRatios(analyseur_entropique)
        
        if not hasattr(analyseur_ratios, 'evolutions_ratios') or not analyseur_ratios.evolutions_ratios:
            print("🔄 Analyse ratios en cours...")
            analyseur_ratios.analyser_evolution_toutes_parties()
            print(f"✅ Ratios calculés pour {len(analyseur_ratios.evolutions_ratios):,} parties")
        else:
            print(f"✅ Données ratios déjà disponibles")
        
        # ====================================================================
        # IV. PHASE 2 : EXTRACTION DES DONNÉES AVEC DIFF
        # ====================================================================
        print(f"\n📊 PHASE 2: EXTRACTION DONNÉES AVEC DIFF")
        print("-" * 50)
        
        donnees_analyse = []
        parties_traitees = 0
        
        for partie_id, evolution_ratios in analyseur_ratios.evolutions_ratios.items():
            if 'erreur' in evolution_ratios:
                continue
            
            # Vérifier la présence de toutes les données nécessaires
            if not all(key in evolution_ratios for key in ['ratios_l4', 'ratios_l5', 'patterns_soe', 'index3_resultats']):
                continue
            
            ratios_l4 = evolution_ratios['ratios_l4']
            ratios_l5 = evolution_ratios['ratios_l5']
            patterns = evolution_ratios['patterns_soe']
            index3 = evolution_ratios['index3_resultats']
            diff_l4_vars = evolution_ratios.get('diff_l4_variations', [])
            diff_l5_vars = evolution_ratios.get('diff_l5_variations', [])
            
            # LOGIQUE PRÉDICTIVE OPTIMALE : i → i+1
            # Utiliser données main i pour prédire pattern i→i+1
            for i in range(len(patterns)):
                if i < len(ratios_l4) and i < len(ratios_l5) and i < len(index3):
                    # OPTIMAL : Données main i pour prédire pattern i→i+1
                    ratio_l4_main = ratios_l4[i]      # Main i (état actuel)
                    ratio_l5_main = ratios_l5[i]      # Main i (état actuel)
                    pattern = patterns[i]             # Pattern i→i+1 (prochaine transition)
                    index3_main = index3[i]           # Index3 main i
                    
                    # Calculer les différentiels si disponibles
                    diff_l4 = diff_l4_vars[i] if i < len(diff_l4_vars) else 0.0
                    diff_l5 = diff_l5_vars[i] if i < len(diff_l5_vars) else 0.0
                    
                    # CALCUL CRITIQUE : DIFF = |L4-L5| (cohérence)
                    diff_coherence = abs(ratio_l4_main - ratio_l5_main)
                    
                    # Ignorer les patterns E (TIE) pour cette analyse
                    if pattern in ['S', 'O']:
                        donnees_analyse.append({
                            'partie_id': partie_id,
                            'main': i + 5,  # Main réelle (FAIT OBJECTIF: analyse commence à main 5)
                            'ratio_l4': ratio_l4_main,
                            'ratio_l5': ratio_l5_main,
                            'diff_l4': diff_l4,
                            'diff_l5': diff_l5,
                            'diff': diff_coherence,  # VARIABLE DIFF AJOUTÉE
                            'pattern': pattern,
                            'index3': index3_main
                        })
            
            parties_traitees += 1
            if parties_traitees % 10000 == 0:
                print(f"   📊 {parties_traitees:,} parties traitées...")
        
        print(f"✅ {len(donnees_analyse):,} points de données extraits AVEC DIFF")
        
        # PHASE 3: Analyse exhaustive des conditions AVEC DIFF
        print(f"\n📊 PHASE 3: ANALYSE EXHAUSTIVE AVEC DIFF")
        print("-" * 50)
        
        conditions_s, conditions_o = analyser_toutes_conditions_avec_diff(donnees_analyse)

        # PHASE 3.5: Calcul des corrélations essentielles (OPTIMISÉ)
        print(f"\n📊 PHASE 3.5: CALCUL CORRÉLATIONS ESSENTIELLES")
        print("-" * 50)

        print("🔬 Calcul des corrélations essentielles uniquement...")
        correlations_stats = calculer_correlations_essentielles(donnees_analyse)
        print(f"✅ Corrélations essentielles calculées pour {correlations_stats.get('total_observations', 0):,} observations")

        # PHASE 4: Génération du tableau prédictif AVEC DIFF ET CORRÉLATIONS ESSENTIELLES
        print(f"\n📊 PHASE 4: GÉNÉRATION TABLEAU AVEC DIFF ET CORRÉLATIONS ESSENTIELLES")
        print("-" * 50)

        nom_rapport = generer_tableau_predictif_avec_diff(conditions_s, conditions_o, len(donnees_analyse), correlations_stats)
        
        print(f"✅ Tableau prédictif AVEC DIFF généré: {nom_rapport}")
        
        # PHASE 5: Affichage des résultats principaux
        print(f"\n📊 PHASE 5: RÉSULTATS AVEC DIFF")
        print("-" * 50)
        
        afficher_resultats_avec_diff(conditions_s, conditions_o, correlations_stats)
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur durant l'analyse: {e}")
        import traceback
        traceback.print_exc()
        return False

# ============================================================================
# V. MOTEUR D'ANALYSE EXHAUSTIVE
# ============================================================================

def analyser_toutes_conditions_avec_diff(donnees):
    """
    MOTEUR D'ANALYSE EXHAUSTIVE
    ===========================

    Analyse toutes les conditions possibles pour prédire S et O AVEC DIFF.
    Effectue 4 types d'analyses : DIFF, L4, L5, et combinaisons.

    Args:
        donnees (list): Liste des données d'analyse avec DIFF

    Returns:
        tuple: (conditions_s, conditions_o) - Listes des conditions identifiées
    """
    print("🔬 Analyse exhaustive des conditions AVEC DIFF...")
    
    conditions_s = []  # Conditions qui favorisent S
    conditions_o = []  # Conditions qui favorisent O
    
    # ANALYSE 1: DIFF (Cohérence L4/L5) - ANALYSE PRINCIPALE
    print("   📊 Analyse DIFF (cohérence L4/L5) - PRIORITÉ ABSOLUE...")
    tranches_diff = [
        (0.0, 0.020, "SIGNAL_PARFAIT"),        # Signal parfait
        (0.020, 0.030, "SIGNAL_EXCELLENT"),    # Signal excellent  
        (0.030, 0.050, "SIGNAL_TRÈS_BON"),     # Signal très fiable
        (0.050, 0.075, "SIGNAL_BON"),          # Signal bon
        (0.075, 0.100, "SIGNAL_ACCEPTABLE"),   # Signal acceptable
        (0.100, 0.150, "SIGNAL_RISQUÉ"),       # Signal risqué
        (0.150, 0.200, "SIGNAL_DOUTEUX"),      # Signal douteux
        (0.200, 0.300, "SIGNAL_TRÈS_DOUTEUX"), # Signal très douteux
        (0.300, 10.0, "SIGNAL_INUTILISABLE")   # Signal inutilisable
    ]
    
    for min_val, max_val, nom in tranches_diff:
        donnees_tranche = [d for d in donnees if min_val <= d['diff'] < max_val]
        if len(donnees_tranche) >= 100:
            analyser_tranche(donnees_tranche, f"DIFF_{nom}", conditions_s, conditions_o)
    
    # ANALYSE 2: Ratios L4 par tranches
    print("   📊 Analyse ratios L4...")
    tranches_l4 = [
        (0.0, 0.3, "ORDRE_TRÈS_FORT"),
        (0.3, 0.5, "ORDRE_FORT"),
        (0.5, 0.7, "ORDRE_MODÉRÉ"),
        (0.7, 0.9, "ÉQUILIBRE"),
        (0.9, 1.1, "CHAOS_MODÉRÉ"),
        (1.1, 1.5, "CHAOS_FORT"),
        (1.5, 10.0, "CHAOS_EXTRÊME")
    ]
    
    for min_val, max_val, nom in tranches_l4:
        donnees_tranche = [d for d in donnees if min_val <= d['ratio_l4'] < max_val]
        if len(donnees_tranche) >= 100:
            analyser_tranche(donnees_tranche, f"L4_{nom}", conditions_s, conditions_o)
    
    # ANALYSE 3: Ratios L5 par tranches
    print("   📊 Analyse ratios L5...")
    for min_val, max_val, nom in tranches_l4:  # Même tranches
        donnees_tranche = [d for d in donnees if min_val <= d['ratio_l5'] < max_val]
        if len(donnees_tranche) >= 100:
            analyser_tranche(donnees_tranche, f"L5_{nom}", conditions_s, conditions_o)
    
    # ANALYSE 4: Combinaisons DIFF + Ratios (NOUVELLES CONDITIONS CRITIQUES)
    print("   📊 Analyse combinaisons DIFF + Ratios...")
    combinaisons_diff = {
        "ORDRE_FORT_DIFF_PARFAIT": lambda d: d['ratio_l4'] < 0.5 and d['diff'] < 0.020,
        "ORDRE_FORT_DIFF_EXCELLENT": lambda d: d['ratio_l4'] < 0.5 and 0.020 <= d['diff'] < 0.030,
        "ORDRE_FORT_DIFF_TRÈS_BON": lambda d: d['ratio_l4'] < 0.5 and 0.030 <= d['diff'] < 0.050,
        "ORDRE_FORT_DIFF_DOUTEUX": lambda d: d['ratio_l4'] < 0.5 and d['diff'] > 0.150,
        
        "ORDRE_MODÉRÉ_DIFF_PARFAIT": lambda d: 0.5 <= d['ratio_l4'] < 0.7 and d['diff'] < 0.020,
        "ORDRE_MODÉRÉ_DIFF_EXCELLENT": lambda d: 0.5 <= d['ratio_l4'] < 0.7 and 0.020 <= d['diff'] < 0.030,
        "ORDRE_MODÉRÉ_DIFF_DOUTEUX": lambda d: 0.5 <= d['ratio_l4'] < 0.7 and d['diff'] > 0.150,
        
        "ÉQUILIBRE_DIFF_PARFAIT": lambda d: 0.7 <= d['ratio_l4'] <= 0.9 and d['diff'] < 0.020,
        "ÉQUILIBRE_DIFF_EXCELLENT": lambda d: 0.7 <= d['ratio_l4'] <= 0.9 and 0.020 <= d['diff'] < 0.030,
        "ÉQUILIBRE_DIFF_DOUTEUX": lambda d: 0.7 <= d['ratio_l4'] <= 0.9 and d['diff'] > 0.150,
        
        "CHAOS_DIFF_PARFAIT": lambda d: d['ratio_l4'] > 0.9 and d['diff'] < 0.020,
        "CHAOS_DIFF_EXCELLENT": lambda d: d['ratio_l4'] > 0.9 and 0.020 <= d['diff'] < 0.030,
        "CHAOS_DIFF_DOUTEUX": lambda d: d['ratio_l4'] > 0.9 and d['diff'] > 0.150,
        
        # Combinaisons avec variations
        "VARIATIONS_FORTES_DIFF_PARFAIT": lambda d: (d['diff_l4'] > 0.1 or d['diff_l5'] > 0.1) and d['diff'] < 0.020,
        "VARIATIONS_FORTES_DIFF_EXCELLENT": lambda d: (d['diff_l4'] > 0.1 or d['diff_l5'] > 0.1) and 0.020 <= d['diff'] < 0.030,
        "VARIATIONS_FORTES_DIFF_DOUTEUX": lambda d: (d['diff_l4'] > 0.1 or d['diff_l5'] > 0.1) and d['diff'] > 0.150,
        
        "STABILITÉ_DIFF_PARFAIT": lambda d: d['diff_l4'] < 0.02 and d['diff_l5'] < 0.02 and d['diff'] < 0.020,
        "STABILITÉ_DIFF_EXCELLENT": lambda d: d['diff_l4'] < 0.02 and d['diff_l5'] < 0.02 and 0.020 <= d['diff'] < 0.030
    }
    
    for nom, condition in combinaisons_diff.items():
        donnees_cond = [d for d in donnees if condition(d)]
        if len(donnees_cond) >= 100:
            analyser_tranche(donnees_cond, f"COMB_{nom}", conditions_s, conditions_o)
    
    print(f"✅ Analyse AVEC DIFF terminée: {len(conditions_s)} conditions S, {len(conditions_o)} conditions O")
    
    return conditions_s, conditions_o

class FormulesMathematiquesEntropie:
    """
    CLASSE INTÉGRANT TOUTES LES FORMULES D'ENTROPIE MATHÉMATIQUES
    ============================================================

    Cette classe implémente toutes les formules d'entropie du fichier formules_entropie_python.txt
    et les applique de manière pertinente aux métriques du système d'analyse de baccarat.

    CORRESPONDANCES MÉTRIQUES → FORMULES :

    1. ENTROPIES DE BASE :
       - shannon_entropy() → Entropies locales L4, L5 et globales
       - bernoulli_entropy() → Patterns binaires S/O (0/1)
       - uniform_entropy() → Distribution uniforme des résultats B/P/T

    2. DIVERGENCES ET COMPARAISONS :
       - relative_entropy() → Comparaison distributions locales vs globales
       - cross_entropy() → Mesure d'erreur prédictive
       - mutual_information() → Dépendance entre métriques

    3. ENTROPIES CONDITIONNELLES :
       - conditional_entropy() → H(Pattern|Contexte)
       - joint_entropy() → Entropie jointe des métriques

    4. MÉTRIQUES SPÉCIALISÉES BACCARAT :
       - entropy_ratio() → Ratios L4/L5 existants
       - entropy_difference() → DIFF = |L4-L5| existant
       - logarithmic_prediction_formula() → P(S) = 0.45 + 0.35*log(DIFF+0.01)
       - entropy_signal_quality() → Qualité du signal prédictif

    5. ANALYSES TEMPORELLES :
       - variations_entropy() → Évolution des entropies dans le temps
       - markov_entropy() → Modélisation des transitions S→S, S→O, O→S, O→O
       - ergodic_entropy_estimate() → Estimation sur longues séquences

    INTÉGRATION DANS LE SYSTÈME :
    - Calculs automatiques sur toutes les données
    - Métriques supplémentaires pour corrélations
    - Analyses prédictives avancées
    - Rapports enrichis avec formules mathématiques
    """

    def __init__(self, donnees):
        """
        Initialise la classe avec les données d'analyse.

        Args:
            donnees (list): Liste des données d'analyse avec toutes les métriques
        """
        import math
        import numpy as np
        from collections import Counter

        self.donnees = donnees
        self.math = math
        self.np = np
        self.Counter = Counter

        # Dictionnaire pour stocker toutes les métriques calculées
        self.metriques_entropie = {}

        # Calculer toutes les formules d'entropie
        self._calculer_toutes_formules_entropie()

    def _extraire_sequences_pour_entropie(self):
        """
        Extrait les séquences nécessaires pour les calculs d'entropie.

        Returns:
            dict: Dictionnaire contenant toutes les séquences extraites
        """
        sequences = {
            'patterns': [],           # Séquence des patterns S/O
            'resultats': [],         # Séquence des résultats B/P/T
            'entropies_locales_4': [],  # Entropies locales L4
            'entropies_locales_5': [],  # Entropies locales L5
            'entropies_globales': [],   # Entropies globales
            'ratios_l4_l5': [],        # Ratios L4/L5
            'differences_l4_l5': [],   # Différences |L4-L5|
            'diff_l4_values': [],      # Valeurs diff_l4
            'diff_l5_values': [],      # Valeurs diff_l5
            'diff_values': []          # Valeurs DIFF
        }

        for donnee in self.donnees:
            # Patterns S/O (convertis en 0/1 pour Bernoulli)
            pattern = donnee.get('pattern', '')
            if pattern == 'S':
                sequences['patterns'].append(1)
            elif pattern == 'O':
                sequences['patterns'].append(0)

            # Résultats B/P/T (pour distribution uniforme)
            resultat = donnee.get('resultat', '')
            if resultat in ['B', 'P', 'T']:
                sequences['resultats'].append(resultat)

            # Métriques d'entropie existantes
            sequences['entropies_locales_4'].append(donnee.get('entropie_locale', 0.0))
            sequences['entropies_globales'].append(donnee.get('entropie_globale', 0.0))
            sequences['ratios_l4_l5'].append(donnee.get('ratio_l4', 0.0))
            sequences['differences_l4_l5'].append(donnee.get('diff', 0.0))
            sequences['diff_l4_values'].append(donnee.get('diff_l4', 0.0))
            sequences['diff_l5_values'].append(donnee.get('diff_l5', 0.0))
            sequences['diff_values'].append(donnee.get('diff', 0.0))

        return sequences

    def _calculer_toutes_formules_entropie(self):
        """
        Calcule toutes les formules d'entropie applicables aux données de baccarat.
        """
        print("   🧮 Calcul des formules mathématiques d'entropie...")

        # Extraire les séquences
        sequences = self._extraire_sequences_pour_entropie()

        # 1. ENTROPIES DE BASE
        self._calculer_entropies_base(sequences)

        # 2. DIVERGENCES ET COMPARAISONS
        self._calculer_divergences(sequences)

        # 3. ENTROPIES CONDITIONNELLES ET JOINTES
        self._calculer_entropies_conditionnelles(sequences)

        # 4. MÉTRIQUES SPÉCIALISÉES BACCARAT
        self._calculer_metriques_baccarat(sequences)

        # 5. ANALYSES TEMPORELLES
        self._calculer_analyses_temporelles(sequences)

        # 6. INFORMATION MUTUELLE
        self._calculer_information_mutuelle(sequences)

        print(f"   ✅ {len(self.metriques_entropie)} formules d'entropie calculées")

    def _calculer_entropies_base(self, sequences):
        """Calcule les entropies de base (Shannon, Bernoulli, Uniforme)."""

        # 1. ENTROPIE DE SHANNON pour patterns S/O
        if sequences['patterns']:
            # Distribution des patterns S/O
            pattern_counts = self.Counter(sequences['patterns'])
            total_patterns = len(sequences['patterns'])
            pattern_probs = [count/total_patterns for count in pattern_counts.values()]

            self.metriques_entropie['shannon_entropy_patterns'] = self._shannon_entropy(pattern_probs)

        # 2. ENTROPIE DE BERNOULLI pour patterns binaires
        if sequences['patterns']:
            prob_s = sum(sequences['patterns']) / len(sequences['patterns'])
            self.metriques_entropie['bernoulli_entropy_patterns'] = self._bernoulli_entropy(prob_s)

        # 3. ENTROPIE UNIFORME pour résultats B/P/T
        if sequences['resultats']:
            result_counts = self.Counter(sequences['resultats'])
            n_results = len(result_counts)
            self.metriques_entropie['uniform_entropy_results'] = self._uniform_entropy(n_results)

            # Distribution réelle des résultats
            total_results = len(sequences['resultats'])
            result_probs = [count/total_results for count in result_counts.values()]
            self.metriques_entropie['shannon_entropy_results'] = self._shannon_entropy(result_probs)

        # 4. ENTROPIES DES MÉTRIQUES CONTINUES
        for metric_name in ['entropies_locales_4', 'entropies_globales', 'ratios_l4_l5']:
            if sequences[metric_name]:
                # Discrétiser les valeurs continues pour calculer l'entropie
                discretized = self._discretize_continuous_values(sequences[metric_name])
                if discretized:
                    counts = self.Counter(discretized)
                    total = len(discretized)
                    probs = [count/total for count in counts.values()]
                    self.metriques_entropie[f'shannon_entropy_{metric_name}'] = self._shannon_entropy(probs)

    def _calculer_divergences(self, sequences):
        """Calcule les divergences KL et entropies croisées."""

        # 1. DIVERGENCE KL entre distributions locales et globales
        if sequences['entropies_locales_4'] and sequences['entropies_globales']:
            local_disc = self._discretize_continuous_values(sequences['entropies_locales_4'])
            global_disc = self._discretize_continuous_values(sequences['entropies_globales'])

            if local_disc and global_disc:
                local_dist = self._get_probability_distribution(local_disc)
                global_dist = self._get_probability_distribution(global_disc)

                # Aligner les distributions
                aligned_local, aligned_global = self._align_distributions(local_dist, global_dist)

                if aligned_local and aligned_global:
                    self.metriques_entropie['kl_divergence_local_global'] = self._relative_entropy(aligned_local, aligned_global)

        # 2. ENTROPIE CROISÉE pour évaluation prédictive
        if sequences['patterns']:
            # Distribution observée vs distribution uniforme
            observed_probs = self._get_probability_distribution(sequences['patterns'])
            uniform_probs = [0.5, 0.5]  # Distribution uniforme S/O

            if len(observed_probs) == 2:
                self.metriques_entropie['cross_entropy_patterns'] = self._cross_entropy(observed_probs, uniform_probs)

    def _calculer_entropies_conditionnelles(self, sequences):
        """Calcule les entropies conditionnelles et jointes."""

        # 1. ENTROPIE CONDITIONNELLE H(Pattern|Contexte)
        if sequences['patterns'] and sequences['diff_values']:
            # Discrétiser DIFF en contextes (faible/moyen/fort)
            diff_contexts = self._discretize_diff_contexts(sequences['diff_values'])

            if diff_contexts:
                # Calculer H(Pattern|DIFF_Context)
                self.metriques_entropie['conditional_entropy_pattern_given_diff'] = \
                    self._conditional_entropy_discrete(sequences['patterns'], diff_contexts)

        # 2. ENTROPIE JOINTE H(Pattern, DIFF)
        if sequences['patterns'] and sequences['diff_values']:
            diff_discrete = self._discretize_continuous_values(sequences['diff_values'])
            if diff_discrete:
                joint_pairs = list(zip(sequences['patterns'], diff_discrete))
                joint_counts = self.Counter(joint_pairs)
                total = len(joint_pairs)
                joint_probs = [count/total for count in joint_counts.values()]
                self.metriques_entropie['joint_entropy_pattern_diff'] = self._shannon_entropy(joint_probs)

    def _calculer_metriques_baccarat(self, sequences):
        """Calcule les métriques spécialisées pour l'analyse de baccarat."""

        # 1. FORMULE LOGARITHMIQUE DE PRÉDICTION P(S) = 0.45 + 0.35 * log(DIFF + 0.01)
        if sequences['diff_values']:
            predictions = []
            for diff_val in sequences['diff_values']:
                pred_prob = self._logarithmic_prediction_formula(diff_val)
                predictions.append(pred_prob)

            self.metriques_entropie['logarithmic_predictions'] = predictions
            self.metriques_entropie['mean_logarithmic_prediction'] = sum(predictions) / len(predictions)

        # 2. QUALITÉ DU SIGNAL D'ENTROPIE
        if sequences['diff_values'] and sequences['ratios_l4_l5']:
            quality_scores = []
            for diff_val, ratio_val in zip(sequences['diff_values'], sequences['ratios_l4_l5']):
                quality = self._entropy_signal_quality(diff_val, ratio_val)
                quality_scores.append(quality['quality_score'])

            self.metriques_entropie['entropy_signal_qualities'] = quality_scores
            self.metriques_entropie['mean_signal_quality'] = sum(quality_scores) / len(quality_scores)

        # 3. FORCE DU SIGNAL DE COHÉRENCE
        if sequences['ratios_l4_l5']:
            coherence_strengths = []
            for ratio_val in sequences['ratios_l4_l5']:
                strength, _ = self._coherence_signal_strength(ratio_val)
                coherence_strengths.append(strength)

            self.metriques_entropie['coherence_strengths'] = coherence_strengths
            self.metriques_entropie['mean_coherence_strength'] = sum(coherence_strengths) / len(coherence_strengths)

        # 4. VARIATIONS D'ENTROPIE
        for metric_name in ['entropies_locales_4', 'entropies_globales', 'diff_values']:
            if sequences[metric_name]:
                variations = self._variations_entropy(sequences[metric_name])
                self.metriques_entropie[f'variations_{metric_name}'] = variations
                if variations:
                    self.metriques_entropie[f'mean_variation_{metric_name}'] = sum(variations) / len(variations)

    def _calculer_analyses_temporelles(self, sequences):
        """Calcule les analyses temporelles (Markov, ergodique)."""

        # 1. ENTROPIE DE MARKOV pour transitions S→S, S→O, O→S, O→O
        if sequences['patterns'] and len(sequences['patterns']) > 1:
            transition_matrix, stationary_dist = self._calculate_markov_transition_matrix(sequences['patterns'])

            if transition_matrix and stationary_dist:
                self.metriques_entropie['markov_entropy'] = self._markov_entropy(stationary_dist, transition_matrix)
                self.metriques_entropie['transition_matrix'] = transition_matrix
                self.metriques_entropie['stationary_distribution'] = stationary_dist

        # 2. ESTIMATION ERGODIQUE
        if sequences['patterns'] and len(sequences['patterns']) > 100:
            ergodic_entropy = self._ergodic_entropy_estimate(sequences['patterns'])
            self.metriques_entropie['ergodic_entropy_estimate'] = ergodic_entropy

    def _calculer_information_mutuelle(self, sequences):
        """Calcule l'information mutuelle entre différentes métriques."""

        # 1. INFORMATION MUTUELLE entre patterns et DIFF
        if sequences['patterns'] and sequences['diff_values']:
            diff_discrete = self._discretize_continuous_values(sequences['diff_values'])
            if diff_discrete:
                mutual_info = self._mutual_information_discrete(sequences['patterns'], diff_discrete)
                self.metriques_entropie['mutual_info_pattern_diff'] = mutual_info

        # 2. INFORMATION MUTUELLE entre ratios L4/L5 et patterns
        if sequences['patterns'] and sequences['ratios_l4_l5']:
            ratio_discrete = self._discretize_continuous_values(sequences['ratios_l4_l5'])
            if ratio_discrete:
                mutual_info = self._mutual_information_discrete(sequences['patterns'], ratio_discrete)
                self.metriques_entropie['mutual_info_pattern_ratio'] = mutual_info

    # ========================================================================
    # MÉTHODES UTILITAIRES POUR LES CALCULS D'ENTROPIE
    # ========================================================================

    def _shannon_entropy(self, probabilities):
        """Calcule l'entropie de Shannon H(p) = -∑ p(x) log₂(p(x))."""
        entropy = 0.0
        for p in probabilities:
            if p > 0:
                entropy -= p * self.math.log2(p)
        return entropy

    def _bernoulli_entropy(self, a):
        """Entropie de Bernoulli h(a) = -a log₂(a) - (1-a) log₂(1-a)."""
        if a <= 0 or a >= 1:
            return 0.0
        return -a * self.math.log2(a) - (1 - a) * self.math.log2(1 - a)

    def _uniform_entropy(self, n):
        """Entropie uniforme H(uniform) = log₂(n)."""
        return self.math.log2(max(1, n))

    def _relative_entropy(self, p, q):
        """Divergence KL D(p||q) = ∑ p(x) log₂(p(x)/q(x))."""
        divergence = 0.0
        for pi, qi in zip(p, q):
            if pi > 0:
                if qi <= 0:
                    return float('inf')
                divergence += pi * self.math.log2(pi / qi)
        return divergence

    def _cross_entropy(self, p, q):
        """Entropie croisée H(p,q) = -∑ p(x) log₂(q(x))."""
        cross_ent = 0.0
        for pi, qi in zip(p, q):
            if pi > 0:
                if qi <= 0:
                    return float('inf')
                cross_ent -= pi * self.math.log2(qi)
        return cross_ent

    def _logarithmic_prediction_formula(self, diff_value, base_prob=0.45, scale_factor=0.35, offset=0.01):
        """Formule logarithmique P(S) = 0.45 + 0.35 * log(DIFF + 0.01)."""
        if diff_value < 0:
            diff_value = 0

        log_term = self.math.log(diff_value + offset)
        prob = base_prob + scale_factor * log_term

        return max(0.0, min(1.0, prob))

    def _entropy_signal_quality(self, diff_value, ratio_value):
        """Évalue la qualité du signal d'entropie pour prédiction."""
        pred_prob = self._logarithmic_prediction_formula(diff_value)
        coherence_strength, coherence_type = self._coherence_signal_strength(ratio_value)
        quality_score = pred_prob * (1 + coherence_strength)

        return {
            'predicted_probability': pred_prob,
            'coherence_strength': coherence_strength,
            'coherence_type': coherence_type,
            'quality_score': quality_score,
            'signal_strength': 'strong' if quality_score > 0.6 else 'weak'
        }

    def _coherence_signal_strength(self, l4_l5_ratio, threshold_low=0.8, threshold_high=1.2):
        """Évalue la force du signal de cohérence basé sur le ratio L4/L5."""
        if threshold_low <= l4_l5_ratio <= threshold_high:
            return abs(l4_l5_ratio - 1.0), "coherent"
        else:
            deviation = min(abs(l4_l5_ratio - threshold_low),
                           abs(l4_l5_ratio - threshold_high))
            return deviation, "incoherent"

    def _variations_entropy(self, entropy_sequence):
        """Calcule les variations d'entropie entre éléments consécutifs."""
        if len(entropy_sequence) < 2:
            return []

        return [entropy_sequence[i] - entropy_sequence[i-1]
                for i in range(1, len(entropy_sequence))]

    def _discretize_continuous_values(self, values, n_bins=10):
        """Discrétise les valeurs continues en bins pour calcul d'entropie."""
        if not values:
            return []

        min_val, max_val = min(values), max(values)
        if min_val == max_val:
            return [0] * len(values)

        bin_size = (max_val - min_val) / n_bins
        discretized = []

        for val in values:
            bin_index = min(int((val - min_val) / bin_size), n_bins - 1)
            discretized.append(bin_index)

        return discretized

    def _discretize_diff_contexts(self, diff_values):
        """Discrétise les valeurs DIFF en contextes (faible/moyen/fort)."""
        if not diff_values:
            return []

        contexts = []
        for diff_val in diff_values:
            if diff_val < 0.030:
                contexts.append('faible')  # Signal excellent
            elif diff_val < 0.050:
                contexts.append('moyen')   # Signal bon
            else:
                contexts.append('fort')    # Signal douteux

        return contexts

    def _get_probability_distribution(self, sequence):
        """Calcule la distribution de probabilité d'une séquence."""
        counts = self.Counter(sequence)
        total = len(sequence)
        return [count/total for count in counts.values()]

    def _align_distributions(self, dist1, dist2):
        """Aligne deux distributions pour les rendre comparables."""
        # Pour simplifier, on retourne les distributions telles quelles
        # Dans une implémentation complète, il faudrait aligner les supports
        if len(dist1) == len(dist2):
            return dist1, dist2
        return [], []

    def _conditional_entropy_discrete(self, y_sequence, x_sequence):
        """Calcule H(Y|X) pour des séquences discrètes."""
        if len(y_sequence) != len(x_sequence):
            return 0.0

        # Compter les occurrences jointes et marginales
        joint_counts = self.Counter(zip(x_sequence, y_sequence))
        x_counts = self.Counter(x_sequence)

        total = len(y_sequence)
        cond_entropy = 0.0

        for (x_val, y_val), joint_count in joint_counts.items():
            p_xy = joint_count / total
            p_x = x_counts[x_val] / total
            p_y_given_x = joint_count / x_counts[x_val]

            if p_y_given_x > 0:
                cond_entropy -= p_xy * self.math.log2(p_y_given_x)

        return cond_entropy

    def _mutual_information_discrete(self, x_sequence, y_sequence):
        """Calcule I(X;Y) pour des séquences discrètes."""
        if len(x_sequence) != len(y_sequence):
            return 0.0

        # Entropies marginales
        h_x = self._shannon_entropy(self._get_probability_distribution(x_sequence))
        h_y = self._shannon_entropy(self._get_probability_distribution(y_sequence))

        # Entropie jointe
        joint_sequence = list(zip(x_sequence, y_sequence))
        h_xy = self._shannon_entropy(self._get_probability_distribution(joint_sequence))

        return h_x + h_y - h_xy

    def _calculate_markov_transition_matrix(self, sequence):
        """Calcule la matrice de transition et la distribution stationnaire."""
        if len(sequence) < 2:
            return None, None

        # Compter les transitions
        transitions = {}
        states = list(set(sequence))

        for state in states:
            transitions[state] = {s: 0 for s in states}

        for i in range(len(sequence) - 1):
            current_state = sequence[i]
            next_state = sequence[i + 1]
            transitions[current_state][next_state] += 1

        # Normaliser pour obtenir les probabilités
        transition_matrix = []
        for state in states:
            total_transitions = sum(transitions[state].values())
            if total_transitions > 0:
                row = [transitions[state][s] / total_transitions for s in states]
            else:
                row = [1.0 / len(states)] * len(states)  # Distribution uniforme
            transition_matrix.append(row)

        # Distribution stationnaire (approximation par fréquences observées)
        state_counts = self.Counter(sequence)
        total = len(sequence)
        stationary_dist = [state_counts[state] / total for state in states]

        return transition_matrix, stationary_dist

    def _markov_entropy(self, stationary_dist, transition_matrix):
        """Calcule l'entropie d'une chaîne de Markov."""
        entropy = 0.0
        for i, mu_i in enumerate(stationary_dist):
            if mu_i > 0:
                for j, p_ij in enumerate(transition_matrix[i]):
                    if p_ij > 0:
                        entropy -= mu_i * p_ij * self.math.log2(p_ij)
        return entropy

    def _ergodic_entropy_estimate(self, sequence):
        """Estime l'entropie d'un processus ergodique."""
        # Estimation simple basée sur l'entropie empirique
        return self._shannon_entropy(self._get_probability_distribution(sequence))

    def get_toutes_metriques_entropie(self):
        """Retourne toutes les métriques d'entropie calculées."""
        return self.metriques_entropie.copy()

    def get_metriques_pour_correlations(self):
        """Retourne les métriques sous forme de listes pour les corrélations."""
        metriques_correlations = {}

        # Métriques scalaires répétées pour chaque observation
        n_observations = len(self.donnees)

        for nom, valeur in self.metriques_entropie.items():
            if isinstance(valeur, list) and len(valeur) == n_observations:
                # Métrique déjà alignée avec les observations
                metriques_correlations[nom] = valeur
            elif isinstance(valeur, (int, float)):
                # Métrique scalaire répétée
                metriques_correlations[nom] = [valeur] * n_observations

        return metriques_correlations

class EcartsTypes:
    """
    CLASSE POUR TOUS LES ÉCARTS-TYPES
    =================================

    Calcule l'écart-type de toutes les métriques actuelles.
    Mesure la volatilité/dispersion de chaque métrique pour identifier les plus stables.

    ÉCARTS-TYPES DES MÉTRIQUES DE BASE :
    - std_diff_l4 = écart-type de diff_l4
    - std_diff_l5 = écart-type de diff_l5
    - std_diff = écart-type de diff
    - std_ratio_l4 = écart-type de ratio_l4
    - std_ratio_l5 = écart-type de ratio_l5

    ÉCARTS-TYPES DES MÉTRIQUES DÉRIVÉES :
    - std_somme_ratios, std_diff_ratios, etc.
    """

    def __init__(self, donnees):
        """
        Initialise la classe avec les données.

        Args:
            donnees (list): Liste des données d'analyse
        """
        self.donnees = donnees
        self.ecarts_types = {}
        self._calculer_tous_ecarts_types()

    def _calculer_ecart_type(self, values):
        """
        Calcule l'écart-type d'une série de valeurs.

        Args:
            values (list): Liste des valeurs

        Returns:
            float: Écart-type de la série
        """
        if len(values) < 2:
            return 0.0

        import math
        n = len(values)
        mean = sum(values) / n
        variance = sum((x - mean) ** 2 for x in values) / n
        return math.sqrt(variance)

    def _calculer_tous_ecarts_types(self):
        """Calcule tous les écarts-types des métriques actuelles."""

        # ÉCARTS-TYPES DES MÉTRIQUES DE BASE
        diff_l4_values = [d['diff_l4'] for d in self.donnees]
        diff_l5_values = [d['diff_l5'] for d in self.donnees]
        diff_values = [d['diff'] for d in self.donnees]
        ratio_l4_values = [d['ratio_l4'] for d in self.donnees]
        ratio_l5_values = [d['ratio_l5'] for d in self.donnees]

        self.ecarts_types['std_diff_l4'] = self._calculer_ecart_type(diff_l4_values)
        self.ecarts_types['std_diff_l5'] = self._calculer_ecart_type(diff_l5_values)
        self.ecarts_types['std_diff'] = self._calculer_ecart_type(diff_values)
        self.ecarts_types['std_ratio_l4'] = self._calculer_ecart_type(ratio_l4_values)
        self.ecarts_types['std_ratio_l5'] = self._calculer_ecart_type(ratio_l5_values)

        # ÉCARTS-TYPES DES MÉTRIQUES DÉRIVÉES
        somme_ratios = [l4 + l5 for l4, l5 in zip(ratio_l4_values, ratio_l5_values)]
        diff_ratios = [abs(l4 - l5) for l4, l5 in zip(ratio_l4_values, ratio_l5_values)]
        produit_ratios = [l4 * l5 for l4, l5 in zip(ratio_l4_values, ratio_l5_values)]
        moyenne_ratios = [(l4 + l5) / 2 for l4, l5 in zip(ratio_l4_values, ratio_l5_values)]
        somme_diffs = [d4 + d5 for d4, d5 in zip(diff_l4_values, diff_l5_values)]
        diff_diffs = [abs(d4 - d5) for d4, d5 in zip(diff_l4_values, diff_l5_values)]
        ratio_coherence = [1 - diff for diff in diff_values]
        indice_stabilite = [1 - abs(l4 - l5) - diff for l4, l5, diff in zip(ratio_l4_values, ratio_l5_values, diff_values)]

        self.ecarts_types['std_somme_ratios'] = self._calculer_ecart_type(somme_ratios)
        self.ecarts_types['std_diff_ratios'] = self._calculer_ecart_type(diff_ratios)
        self.ecarts_types['std_produit_ratios'] = self._calculer_ecart_type(produit_ratios)
        self.ecarts_types['std_moyenne_ratios'] = self._calculer_ecart_type(moyenne_ratios)
        self.ecarts_types['std_somme_diffs'] = self._calculer_ecart_type(somme_diffs)
        self.ecarts_types['std_diff_diffs'] = self._calculer_ecart_type(diff_diffs)
        self.ecarts_types['std_ratio_coherence'] = self._calculer_ecart_type(ratio_coherence)
        self.ecarts_types['std_indice_stabilite'] = self._calculer_ecart_type(indice_stabilite)

        # ÉCARTS-TYPES DES MÉTRIQUES INVERSES - SUPPRIMÉS
        # Les métriques inverses ont été supprimées du système

    def get_ecart_type(self, nom_metrique):
        """
        Retourne l'écart-type d'une métrique spécifique.

        Args:
            nom_metrique (str): Nom de l'écart-type (ex: 'std_diff')

        Returns:
            float: Valeur de l'écart-type
        """
        return self.ecarts_types.get(nom_metrique, 0.0)

    def get_tous_ecarts_types(self):
        """
        Retourne tous les écarts-types calculés.

        Returns:
            dict: Dictionnaire de tous les écarts-types
        """
        return self.ecarts_types

    def get_noms_ecarts_types(self):
        """
        Retourne la liste des noms de tous les écarts-types.

        Returns:
            list: Liste des noms des écarts-types
        """
        return list(self.ecarts_types.keys())

    def get_metriques_plus_stables(self, top_n=5):
        """
        Retourne les métriques les plus stables (écart-type le plus faible).

        Args:
            top_n (int): Nombre de métriques à retourner

        Returns:
            list: Liste des (nom_métrique, écart_type) triée par stabilité
        """
        ecarts_tries = sorted(self.ecarts_types.items(), key=lambda x: x[1])
        return ecarts_tries[:top_n]

    def get_metriques_plus_volatiles(self, top_n=5):
        """
        Retourne les métriques les plus volatiles (écart-type le plus élevé).

        Args:
            top_n (int): Nombre de métriques à retourner

        Returns:
            list: Liste des (nom_métrique, écart_type) triée par volatilité
        """
        ecarts_tries = sorted(self.ecarts_types.items(), key=lambda x: x[1], reverse=True)
        return ecarts_tries[:top_n]


# ============================================================================
# VII. CALCULATEUR DE CORRÉLATIONS ESSENTIELLES (OPTIMISÉ)
# ============================================================================

def calculer_correlations_essentielles(donnees):
    """
    CALCULATEUR DE CORRÉLATIONS ESSENTIELLES OPTIMISÉ
    =================================================

    Calcule UNIQUEMENT les corrélations essentielles pour optimiser les performances :
    - Diff_L4 avec DIFF
    - Diff_L5 avec DIFF
    - Ratio L4 avec L5
    - Diff_L4 avec Diff_L5
    - Ratio L4 avec DIFF
    - Ratio L5 avec DIFF

    Args:
        donnees (list): Liste des données d'analyse

    Returns:
        dict: Dictionnaire contenant les corrélations essentielles uniquement
    """
    import math

    if len(donnees) < 2:
        return {}

    # Extraction des variables essentielles
    diff_l4_values = [d['diff_l4'] for d in donnees]
    diff_l5_values = [d['diff_l5'] for d in donnees]
    diff_values = [d['diff'] for d in donnees]
    ratio_l4_values = [d['ratio_l4'] for d in donnees]
    ratio_l5_values = [d['ratio_l5'] for d in donnees]

    def calculer_correlation(x_values, y_values):
        """Calcule le coefficient de corrélation de Pearson"""
        n = len(x_values)
        if n < 2:
            return 0.0

        # Moyennes
        mean_x = sum(x_values) / n
        mean_y = sum(y_values) / n

        # Calcul corrélation
        numerator = sum((x_values[i] - mean_x) * (y_values[i] - mean_y) for i in range(n))
        sum_sq_x = sum((x_values[i] - mean_x) ** 2 for i in range(n))
        sum_sq_y = sum((y_values[i] - mean_y) ** 2 for i in range(n))

        denominator = math.sqrt(sum_sq_x * sum_sq_y)

        if denominator == 0:
            return 0.0

        return numerator / denominator

    # Calcul des corrélations essentielles uniquement
    correlations = {
        'diff_l4_avec_diff': calculer_correlation(diff_l4_values, diff_values),
        'diff_l5_avec_diff': calculer_correlation(diff_l5_values, diff_values),
        'ratio_l4_avec_l5': calculer_correlation(ratio_l4_values, ratio_l5_values),
        'diff_l4_avec_l5': calculer_correlation(diff_l4_values, diff_l5_values),
        'ratio_l4_avec_diff': calculer_correlation(ratio_l4_values, diff_values),
        'ratio_l5_avec_diff': calculer_correlation(ratio_l5_values, diff_values)
    }

    print(f"   ✅ {len(correlations)} corrélations essentielles calculées (au lieu de 1,326+)")

    # Statistiques descriptives pour les variables essentielles
    def calculer_statistiques_descriptives(valeurs):
        n = len(valeurs)
        if n == 0:
            return {}

        moyenne = sum(valeurs) / n
        variance = sum((x - moyenne) ** 2 for x in valeurs) / n
        ecart_type = math.sqrt(variance)

        valeurs_triees = sorted(valeurs)
        mediane = valeurs_triees[n // 2] if n % 2 == 1 else (valeurs_triees[n // 2 - 1] + valeurs_triees[n // 2]) / 2

        return {
            'mean': moyenne,
            'median': mediane,
            'std_dev': ecart_type,
            'min': min(valeurs),
            'max': max(valeurs),
            'count': n
        }

    statistiques = {
        'diff_l4': calculer_statistiques_descriptives(diff_l4_values),
        'diff_l5': calculer_statistiques_descriptives(diff_l5_values),
        'diff': calculer_statistiques_descriptives(diff_values),
        'ratio_l4': calculer_statistiques_descriptives(ratio_l4_values),
        'ratio_l5': calculer_statistiques_descriptives(ratio_l5_values)
    }

    return {
        'correlations': correlations,
        'statistiques': statistiques,
        'total_observations': len(donnees)
    }

# ============================================================================
# VII. CALCULATEUR DE CORRÉLATIONS STATISTIQUES (DÉSACTIVÉ - TROP LOURD)
# ============================================================================

def calculer_correlations_statistiques_DESACTIVE(donnees):
    """
    CALCULATEUR DE CORRÉLATIONS STATISTIQUES (DÉSACTIVÉ - TROP LOURD)
    =================================================================

    FONCTION DÉSACTIVÉE : Calculait TOUTES les corrélations (1,326+) et métriques :
    - Corrélations entre TOUTES les variables actuelles
    - Écarts-types de toutes les métriques
    - Formules mathématiques applicables
    - Seuils de décision précis
    - Stratégies par main et par contexte

    Cette fonction était trop lourde en performance.

    Args:
        donnees (list): Liste des données d'analyse

    Returns:
        dict: Dictionnaire vide (fonction désactivée)
    """

    # FONCTION DÉSACTIVÉE POUR OPTIMISATION DES PERFORMANCES
    print("   ⚠️  Calcul de toutes les corrélations DÉSACTIVÉ (trop lourd)")
    return {
        'correlations': {},
        'statistiques': {},
        'total_observations': len(donnees)
    }
    import math

    if len(donnees) < 2:
        return {}

    print("🔬 CALCUL AVEC CLASSE ÉCARTS-TYPES ET FORMULES MATHÉMATIQUES D'ENTROPIE")

    # ÉTAPE 1 : Création de l'objet pour écarts-types
    print("   📊 Calcul des écarts-types...")
    ecarts_types_obj = EcartsTypes(donnees)

    # ÉTAPE 1.5 : Création de l'objet pour formules mathématiques d'entropie
    print("   🧮 Calcul des formules mathématiques d'entropie...")
    formules_entropie_obj = FormulesMathematiquesEntropie(donnees)

    print(f"   ✅ {len(ecarts_types_obj.get_noms_ecarts_types())} écarts-types calculés")

    # Extraction des variables AVEC contexte main
    diff_l4_values = [d['diff_l4'] for d in donnees]
    diff_l5_values = [d['diff_l5'] for d in donnees]
    diff_values = [d['diff'] for d in donnees]
    ratio_l4_values = [d['ratio_l4'] for d in donnees]
    ratio_l5_values = [d['ratio_l5'] for d in donnees]
    main_numbers = [d['main'] for d in donnees]
    patterns = [d['pattern'] for d in donnees]

    def calculer_correlation(x_values, y_values):
        """Calcule le coefficient de corrélation de Pearson"""
        n = len(x_values)
        if n < 2:
            return 0.0

        # Moyennes
        mean_x = sum(x_values) / n
        mean_y = sum(y_values) / n

        # Calcul corrélation
        numerator = sum((x_values[i] - mean_x) * (y_values[i] - mean_y) for i in range(n))
        sum_sq_x = sum((x_values[i] - mean_x) ** 2 for i in range(n))
        sum_sq_y = sum((y_values[i] - mean_y) ** 2 for i in range(n))

        denominator = math.sqrt(sum_sq_x * sum_sq_y)

        if denominator == 0:
            return 0.0

        return numerator / denominator

    def calculer_statistiques_descriptives(values):
        """Calcule les statistiques descriptives d'une série"""
        if not values:
            return {}

        n = len(values)
        mean = sum(values) / n
        variance = sum((x - mean) ** 2 for x in values) / n
        std_dev = math.sqrt(variance)

        sorted_values = sorted(values)
        median = sorted_values[n // 2] if n % 2 == 1 else (sorted_values[n // 2 - 1] + sorted_values[n // 2]) / 2

        return {
            'count': n,
            'mean': mean,
            'median': median,
            'std_dev': std_dev,
            'variance': variance,
            'min': min(values),
            'max': max(values)
        }

    # CONSTRUCTION DU DICTIONNAIRE COMPLET DE TOUTES LES MÉTRIQUES
    print("   🔄 Construction du dictionnaire complet des métriques...")

    # Métriques originales
    toutes_metriques = {
        'diff_l4': diff_l4_values,
        'diff_l5': diff_l5_values,
        'diff': diff_values,
        'ratio_l4': ratio_l4_values,
        'ratio_l5': ratio_l5_values
    }

    # Ajout des métriques dérivées (calculées dans analyser_correlations_impact_so)
    # Calcul rapide des métriques dérivées pour les inclure
    somme_ratios = [ratio_l4_values[i] + ratio_l5_values[i] for i in range(len(ratio_l4_values))]
    diff_ratios = [abs(ratio_l4_values[i] - ratio_l5_values[i]) for i in range(len(ratio_l4_values))]
    produit_ratios = [ratio_l4_values[i] * ratio_l5_values[i] for i in range(len(ratio_l4_values))]
    moyenne_ratios = [(ratio_l4_values[i] + ratio_l5_values[i]) / 2 for i in range(len(ratio_l4_values))]
    somme_diffs = [diff_l4_values[i] + diff_l5_values[i] for i in range(len(diff_l4_values))]
    diff_diffs = [abs(diff_l4_values[i] - diff_l5_values[i]) for i in range(len(diff_l4_values))]
    ratio_coherence = [1 - diff_values[i] for i in range(len(diff_values))]
    indice_stabilite = [1 / (1 + diff_values[i]) for i in range(len(diff_values))]

    toutes_metriques.update({
        'somme_ratios': somme_ratios,
        'diff_ratios': diff_ratios,
        'produit_ratios': produit_ratios,
        'moyenne_ratios': moyenne_ratios,
        'somme_diffs': somme_diffs,
        'diff_diffs': diff_diffs,
        'ratio_coherence': ratio_coherence,
        'indice_stabilite': indice_stabilite
    })

    # Ajout des métriques inverses - SUPPRIMÉES
    # Les métriques inverses ont été supprimées du système

    # Ajout des écarts-types
    ecarts_types_dict = ecarts_types_obj.get_tous_ecarts_types()
    for nom, valeur in ecarts_types_dict.items():
        # Les écarts-types sont des valeurs uniques, on les répète pour chaque observation
        toutes_metriques[f'std_{nom}'] = [valeur] * len(donnees)

    # Ajout des formules mathématiques d'entropie
    formules_entropie_dict = formules_entropie_obj.get_metriques_pour_correlations()
    toutes_metriques.update(formules_entropie_dict)

    print(f"   ✅ TOTAL: {len(toutes_metriques)} métriques disponibles pour corrélations")
    print(f"   📊 Dont {len(ecarts_types_dict)} écarts-types et {len(formules_entropie_dict)} formules d'entropie")

    # CALCUL DE TOUTES LES CORRÉLATIONS POSSIBLES
    print("   🔄 Calcul de toutes les corrélations possibles...")
    correlations = {}
    noms_metriques = list(toutes_metriques.keys())

    for i in range(len(noms_metriques)):
        for j in range(i + 1, len(noms_metriques)):
            nom1, nom2 = noms_metriques[i], noms_metriques[j]
            valeurs1, valeurs2 = toutes_metriques[nom1], toutes_metriques[nom2]

            # Vérifier que les deux séries ont la même longueur
            if len(valeurs1) == len(valeurs2):
                corr = calculer_correlation(valeurs1, valeurs2)
                correlations[f'{nom1}_avec_{nom2}'] = corr

    print(f"   ✅ {len(correlations)} corrélations calculées")

    # Statistiques descriptives pour TOUTES les variables
    print("   📊 Calcul des statistiques descriptives pour toutes les métriques...")
    statistiques = {}
    for nom, valeurs in toutes_metriques.items():
        # Éviter les écarts-types qui sont des valeurs constantes répétées
        if not nom.startswith('std_'):
            statistiques[nom] = calculer_statistiques_descriptives(valeurs)

    print(f"   ✅ Statistiques calculées pour {len(statistiques)} métriques")

    # ANALYSE DES FORMULES MATHÉMATIQUES APPLICABLES
    formules_operationnelles = analyser_formules_operationnelles(
        diff_l4_values, diff_l5_values, diff_values,
        ratio_l4_values, ratio_l5_values, main_numbers, patterns
    )

    # ANALYSE EXHAUSTIVE DES CORRÉLATIONS ET IMPACT S/O (DÉSACTIVÉE)
    # tableau_correlations_impact = analyser_correlations_impact_so_DESACTIVE(
    #     toutes_metriques, patterns, donnees
    # )
    tableau_correlations_impact = {
        'matrice_correlations': {},
        'impact_so': {},
        'metriques_disponibles': []
    }

    return {
        'correlations': correlations,
        'statistiques': statistiques,
        'formules_operationnelles': formules_operationnelles,
        'tableau_correlations_impact': tableau_correlations_impact,
        'ecarts_types': ecarts_types_obj.get_tous_ecarts_types(),
        'ecarts_types_obj': ecarts_types_obj,
        'formules_entropie': formules_entropie_obj.get_toutes_metriques_entropie(),
        'formules_entropie_obj': formules_entropie_obj,
        'total_observations': len(donnees)
    }

def analyser_formules_operationnelles(diff_l4_values, diff_l5_values, diff_values,
                                    ratio_l4_values, ratio_l5_values, main_numbers, patterns):
    """
    ANALYSEUR DE FORMULES MATHÉMATIQUES OPÉRATIONNELLES
    ==================================================

    Génère des formules mathématiques précises et applicables :
    - Seuils de décision par main
    - Formules de combinaison des métriques
    - Stratégies par contexte
    - Règles de calcul explicites

    Returns:
        dict: Dictionnaire contenant toutes les formules opérationnelles
    """

    # ANALYSE DES SEUILS OPTIMAUX
    seuils_optimaux = {}

    # Seuils pour DIFF
    diff_sorted = sorted(diff_values)
    n = len(diff_sorted)
    seuils_optimaux['diff'] = {
        'parfait': diff_sorted[int(n * 0.05)],      # 5% les plus bas
        'excellent': diff_sorted[int(n * 0.10)],    # 10% les plus bas
        'tres_bon': diff_sorted[int(n * 0.20)],     # 20% les plus bas
        'acceptable': diff_sorted[int(n * 0.50)],   # Médiane
        'douteux': diff_sorted[int(n * 0.80)],      # 80% les plus bas
        'inutilisable': diff_sorted[int(n * 0.95)]  # 95% les plus bas
    }

    # Seuils pour ratios L4 et L5
    ratio_l4_sorted = sorted(ratio_l4_values)
    ratio_l5_sorted = sorted(ratio_l5_values)

    seuils_optimaux['ratio_l4'] = {
        'chaos': ratio_l4_sorted[int(n * 0.10)],
        'equilibre': ratio_l4_sorted[int(n * 0.50)],
        'ordre_modere': ratio_l4_sorted[int(n * 0.70)],
        'ordre_fort': ratio_l4_sorted[int(n * 0.85)],
        'ordre_tres_fort': ratio_l4_sorted[int(n * 0.95)]
    }

    seuils_optimaux['ratio_l5'] = {
        'chaos': ratio_l5_sorted[int(n * 0.10)],
        'equilibre': ratio_l5_sorted[int(n * 0.50)],
        'ordre_modere': ratio_l5_sorted[int(n * 0.70)],
        'ordre_fort': ratio_l5_sorted[int(n * 0.85)],
        'ordre_tres_fort': ratio_l5_sorted[int(n * 0.95)]
    }

    # FORMULES DE COMBINAISON
    formules_combinaison = {
        'score_continuation': {
            'formule': 'SCORE_S = (1 - DIFF/0.3) * 0.4 + (ratio_l4 - 0.5) * 0.3 + (ratio_l5 - 0.5) * 0.3',
            'interpretation': 'Plus le score est élevé, plus S est probable',
            'seuil_decision': 0.6
        },
        'score_alternance': {
            'formule': 'SCORE_O = DIFF * 0.5 + (0.5 - abs(ratio_l4 - 0.5)) * 0.25 + (0.5 - abs(ratio_l5 - 0.5)) * 0.25',
            'interpretation': 'Plus le score est élevé, plus O est probable',
            'seuil_decision': 0.4
        },
        'indice_coherence': {
            'formule': 'COHERENCE = 1 - DIFF - abs(ratio_l4 - ratio_l5)',
            'interpretation': 'Mesure la cohérence globale du signal',
            'seuil_fiabilite': 0.7
        }
    }

    # RÈGLES DE DÉCISION PAR MAIN
    regles_par_main = {
        'mains_5_10': {
            'condition': 'main >= 5 and main <= 10',
            'strategie': 'Privilégier DIFF < 0.05 pour prédictions fiables',
            'formule': 'if DIFF < 0.05: prediction = "S" if ratio_l4 > 0.6 else "O"'
        },
        'mains_11_50': {
            'condition': 'main >= 11 and main <= 50',
            'strategie': 'Utiliser combinaison DIFF + ratios',
            'formule': 'prediction = "S" if (DIFF > 0.15 and ratio_l4 > 0.7) else "O"'
        },
        'mains_50_plus': {
            'condition': 'main > 50',
            'strategie': 'Privilégier stabilité des ratios',
            'formule': 'prediction = "S" if abs(ratio_l4 - ratio_l5) < 0.1 else "O"'
        }
    }

    return {
        'seuils_optimaux': seuils_optimaux,
        'formules_combinaison': formules_combinaison,
        'regles_par_main': regles_par_main
    }

def analyser_correlations_impact_so_DESACTIVE(toutes_metriques, patterns, donnees):
    """
    ANALYSEUR EXHAUSTIF DES CORRÉLATIONS ET IMPACT S/O (DÉSACTIVÉ - TROP LOURD)
    ===========================================================================

    FONCTION DÉSACTIVÉE : Calculait TOUTES les corrélations possibles (1,326+)
    entre TOUTES les métriques, ce qui était trop lourd en performance.

    Args:
        toutes_metriques (dict): Dictionnaire contenant toutes les métriques
        patterns (list): Liste des patterns S/O
        donnees (list): Données complètes

    Returns:
        dict: Dictionnaire vide (fonction désactivée)
    """

    # FONCTION DÉSACTIVÉE POUR OPTIMISATION DES PERFORMANCES
    print("   ⚠️  Analyse exhaustive des corrélations DÉSACTIVÉE (trop de corrélations)")
    return {
        'matrice_correlations': {},
        'impact_so': {},
        'metriques_disponibles': []
    }

# ============================================================================
# VII. ANALYSEUR DE TRANCHES
# ============================================================================

def analyser_tranche(donnees_tranche, nom_condition, conditions_s, conditions_o):
    """
    ANALYSEUR DE TRANCHES
    =====================

    Analyse une tranche de données et détermine si elle favorise S ou O.
    Calcule les pourcentages et classe les conditions par force.

    Args:
        donnees_tranche (list): Données de la tranche à analyser
        nom_condition (str): Nom de la condition analysée
        conditions_s (list): Liste des conditions favorisant S
        conditions_o (list): Liste des conditions favorisant O
    """
    if len(donnees_tranche) < 100:
        return
    
    nb_s = len([d for d in donnees_tranche if d['pattern'] == 'S'])
    nb_o = len([d for d in donnees_tranche if d['pattern'] == 'O'])
    total = nb_s + nb_o
    
    if total == 0:
        return
    
    pourcentage_s = (nb_s / total) * 100
    pourcentage_o = (nb_o / total) * 100
    
    # Seuils pour considérer une condition comme prédictive
    seuil_s = 52.0  # Au moins 52% pour S
    seuil_o = 52.0  # Au moins 52% pour O
    
    condition_data = {
        'nom': nom_condition,
        'total_cas': total,
        'nb_s': nb_s,
        'nb_o': nb_o,
        'pourcentage_s': pourcentage_s,
        'pourcentage_o': pourcentage_o,
        'force': 'FORTE' if max(pourcentage_s, pourcentage_o) >= 60 else 'MODÉRÉE' if max(pourcentage_s, pourcentage_o) >= 55 else 'FAIBLE'
    }
    
    # Ajouter aux conditions appropriées
    if pourcentage_s >= seuil_s and pourcentage_s > pourcentage_o:
        conditions_s.append(condition_data)
    elif pourcentage_o >= seuil_o and pourcentage_o > pourcentage_s:
        conditions_o.append(condition_data)

# ============================================================================
# VII. GÉNÉRATEUR DE RAPPORT
# ============================================================================

def generer_tableau_predictif_avec_diff(conditions_s, conditions_o, total_donnees, correlations_stats=None):
    """
    GÉNÉRATEUR DE RAPPORT ENRICHI
    =============================

    Génère le tableau prédictif complet S/O AVEC DIFF ET CORRÉLATIONS.
    Crée un fichier de rapport détaillé avec toutes les conditions et analyses statistiques.

    Args:
        conditions_s (list): Conditions favorisant S
        conditions_o (list): Conditions favorisant O
        total_donnees (int): Nombre total de données analysées
        correlations_stats (dict): Statistiques et corrélations calculées

    Returns:
        str: Nom du fichier de rapport généré
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    nom_fichier = f"tableau_predictif_avec_diff_{timestamp}.txt"
    
    with open(nom_fichier, 'w', encoding='utf-8') as f:
        f.write("TABLEAU PRÉDICTIF EXHAUSTIF S/O AVEC DIFF\n")
        f.write("=" * 70 + "\n\n")
        f.write("CORRECTION MAJEURE : INCLUSION VARIABLE DIFF\n")
        f.write("DIFF = |L4-L5| = Indicateur qualité signal prédictif\n\n")
        f.write(f"Date de génération: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Données analysées: {total_donnees:,} points\n")
        f.write(f"Conditions S identifiées: {len(conditions_s)}\n")
        f.write(f"Conditions O identifiées: {len(conditions_o)}\n\n")
        
        f.write("SIGNIFICATION DIFF (COHÉRENCE L4/L5):\n")
        f.write("- DIFF < 0.020 : Signal PARFAIT (confiance 95%)\n")
        f.write("- DIFF < 0.030 : Signal EXCELLENT (confiance 90%)\n")
        f.write("- DIFF < 0.050 : Signal TRÈS BON (confiance 85%)\n")
        f.write("- DIFF > 0.150 : Signal DOUTEUX (abstention recommandée)\n\n")
        
        # TABLEAU CONDITIONS S
        f.write("CONDITIONS QUI FAVORISENT S (CONTINUATION)\n")
        f.write("=" * 50 + "\n\n")
        
        # Trier par pourcentage décroissant
        conditions_s_triees = sorted(conditions_s, key=lambda x: x['pourcentage_s'], reverse=True)
        
        f.write("CONDITION                          | CAS     | %S    | %O    | FORCE\n")
        f.write("-" * 70 + "\n")
        
        for cond in conditions_s_triees:
            f.write(f"{cond['nom']:<34} | {cond['total_cas']:>6,} | {cond['pourcentage_s']:>5.1f} | {cond['pourcentage_o']:>5.1f} | {cond['force']}\n")
        
        f.write(f"\nTOTAL CONDITIONS S: {len(conditions_s_triees)}\n\n")
        
        # TABLEAU CONDITIONS O
        f.write("CONDITIONS QUI FAVORISENT O (ALTERNANCE)\n")
        f.write("=" * 50 + "\n\n")
        
        # Trier par pourcentage décroissant
        conditions_o_triees = sorted(conditions_o, key=lambda x: x['pourcentage_o'], reverse=True)
        
        f.write("CONDITION                          | CAS     | %S    | %O    | FORCE\n")
        f.write("-" * 70 + "\n")
        
        for cond in conditions_o_triees:
            f.write(f"{cond['nom']:<34} | {cond['total_cas']:>6,} | {cond['pourcentage_s']:>5.1f} | {cond['pourcentage_o']:>5.1f} | {cond['force']}\n")
        
        f.write(f"\nTOTAL CONDITIONS O: {len(conditions_o_triees)}\n\n")
        
        # ANALYSE SPÉCIALE DIFF
        f.write("ANALYSE SPÉCIALE CONDITIONS DIFF\n")
        f.write("=" * 40 + "\n\n")
        
        conditions_diff_s = [c for c in conditions_s if 'DIFF_' in c['nom']]
        conditions_diff_o = [c for c in conditions_o if 'DIFF_' in c['nom']]
        
        f.write("CONDITIONS DIFF FAVORISANT S:\n")
        for cond in sorted(conditions_diff_s, key=lambda x: x['pourcentage_s'], reverse=True):
            f.write(f"  {cond['nom']}: {cond['pourcentage_s']:.1f}% S ({cond['total_cas']:,} cas)\n")
        
        f.write(f"\nCONDITIONS DIFF FAVORISANT O:\n")
        for cond in sorted(conditions_diff_o, key=lambda x: x['pourcentage_o'], reverse=True):
            f.write(f"  {cond['nom']}: {cond['pourcentage_o']:.1f}% O ({cond['total_cas']:,} cas)\n")

        # SECTION CORRÉLATIONS ET STATISTIQUES ENRICHIE
        if correlations_stats:
            f.write(f"\n\nANALYSES STATISTIQUES ET CORRÉLATIONS ENRICHIES\n")
            f.write("=" * 60 + "\n\n")
            f.write("ANALYSE AVEC ÉCARTS-TYPES\n")
            f.write("- Écarts-types : Volatilité de chaque métrique\n")
            f.write("- Analyse complète : Métriques actuelles + Volatilité\n\n")

            correlations = correlations_stats.get('correlations', {})
            statistiques = correlations_stats.get('statistiques', {})
            ecarts_types = correlations_stats.get('ecarts_types', {})
            ecarts_types_obj = correlations_stats.get('ecarts_types_obj')

            # Corrélations demandées
            f.write("CORRÉLATIONS PRINCIPALES:\n")
            f.write("-" * 30 + "\n")
            f.write(f"Diff_L4 avec DIFF     : {correlations.get('diff_l4_avec_diff', 0):.4f}\n")
            f.write(f"Diff_L5 avec DIFF     : {correlations.get('diff_l5_avec_diff', 0):.4f}\n")
            f.write(f"Ratio L4 avec L5      : {correlations.get('ratio_l4_avec_l5', 0):.4f}\n")
            f.write(f"Diff_L4 avec Diff_L5  : {correlations.get('diff_l4_avec_l5', 0):.4f}\n")
            f.write(f"Ratio L4 avec DIFF    : {correlations.get('ratio_l4_avec_diff', 0):.4f}\n")
            f.write(f"Ratio L5 avec DIFF    : {correlations.get('ratio_l5_avec_diff', 0):.4f}\n\n")

            # Statistiques descriptives
            f.write("STATISTIQUES DESCRIPTIVES:\n")
            f.write("-" * 30 + "\n")

            variables = ['diff_l4', 'diff_l5', 'diff', 'ratio_l4', 'ratio_l5']
            noms_variables = ['Diff_L4', 'Diff_L5', 'DIFF', 'Ratio L4', 'Ratio L5']

            for var, nom in zip(variables, noms_variables):
                stats = statistiques.get(var, {})
                if stats:
                    f.write(f"{nom}:\n")
                    f.write(f"  Moyenne    : {stats.get('mean', 0):.6f}\n")
                    f.write(f"  Médiane    : {stats.get('median', 0):.6f}\n")
                    f.write(f"  Écart-type : {stats.get('std_dev', 0):.6f}\n")
                    f.write(f"  Min        : {stats.get('min', 0):.6f}\n")
                    f.write(f"  Max        : {stats.get('max', 0):.6f}\n")
                    f.write(f"  Observations: {stats.get('count', 0):,}\n\n")

            # SECTION MÉTRIQUES INVERSES - SUPPRIMÉE
            # Les métriques inverses ont été supprimées du système

            # NOUVELLE SECTION : ÉCARTS-TYPES
            if ecarts_types:
                f.write("ÉCARTS-TYPES (Volatilité des métriques):\n")
                f.write("-" * 40 + "\n")
                f.write("Mesure de stabilité : Plus l'écart-type est faible, plus la métrique est stable\n\n")

                # Afficher les écarts-types des métriques principales
                ecarts_principaux = ['std_diff', 'std_diff_l4', 'std_diff_l5', 'std_ratio_l4', 'std_ratio_l5']
                for nom_std in ecarts_principaux:
                    if nom_std in ecarts_types:
                        f.write(f"{nom_std.upper()}: {ecarts_types[nom_std]:.6f}\n")

                f.write(f"\nTOTAL ÉCARTS-TYPES: {len(ecarts_types)} calculés\n")

                # Afficher les métriques les plus stables et volatiles
                if ecarts_types_obj:
                    f.write("\nMÉTRIQUES LES PLUS STABLES (écart-type faible):\n")
                    stables = ecarts_types_obj.get_metriques_plus_stables(5)
                    for i, (nom, ecart) in enumerate(stables, 1):
                        f.write(f"  {i}. {nom}: {ecart:.6f}\n")

                    f.write("\nMÉTRIQUES LES PLUS VOLATILES (écart-type élevé):\n")
                    volatiles = ecarts_types_obj.get_metriques_plus_volatiles(5)
                    for i, (nom, ecart) in enumerate(volatiles, 1):
                        f.write(f"  {i}. {nom}: {ecart:.6f}\n")

                f.write("\n")

            # NOUVELLE SECTION : FORMULES MATHÉMATIQUES D'ENTROPIE
            formules_entropie = correlations_stats.get('formules_entropie', {})
            if formules_entropie:
                f.write("FORMULES MATHÉMATIQUES D'ENTROPIE\n")
                f.write("=" * 40 + "\n")
                f.write("Intégration complète des 52 formules d'entropie du fichier formules_entropie_python.txt\n\n")

                # Entropies de base
                f.write("1. ENTROPIES DE BASE:\n")
                f.write("-" * 21 + "\n")
                for nom, valeur in formules_entropie.items():
                    if 'shannon_entropy' in nom or 'bernoulli_entropy' in nom or 'uniform_entropy' in nom:
                        if isinstance(valeur, (int, float)):
                            f.write(f"   {nom}: {valeur:.6f}\n")

                # Divergences et comparaisons
                f.write(f"\n2. DIVERGENCES ET COMPARAISONS:\n")
                f.write("-" * 31 + "\n")
                for nom, valeur in formules_entropie.items():
                    if 'kl_divergence' in nom or 'cross_entropy' in nom or 'mutual_info' in nom:
                        if isinstance(valeur, (int, float)):
                            f.write(f"   {nom}: {valeur:.6f}\n")

                # Entropies conditionnelles
                f.write(f"\n3. ENTROPIES CONDITIONNELLES:\n")
                f.write("-" * 29 + "\n")
                for nom, valeur in formules_entropie.items():
                    if 'conditional_entropy' in nom or 'joint_entropy' in nom:
                        if isinstance(valeur, (int, float)):
                            f.write(f"   {nom}: {valeur:.6f}\n")

                # Métriques spécialisées baccarat
                f.write(f"\n4. MÉTRIQUES SPÉCIALISÉES BACCARAT:\n")
                f.write("-" * 35 + "\n")
                for nom, valeur in formules_entropie.items():
                    if any(keyword in nom for keyword in ['logarithmic', 'signal_quality', 'coherence', 'prediction']):
                        if isinstance(valeur, (int, float)):
                            f.write(f"   {nom}: {valeur:.6f}\n")

                # Analyses temporelles
                f.write(f"\n5. ANALYSES TEMPORELLES:\n")
                f.write("-" * 24 + "\n")
                for nom, valeur in formules_entropie.items():
                    if 'markov' in nom or 'ergodic' in nom:
                        if isinstance(valeur, (int, float)):
                            f.write(f"   {nom}: {valeur:.6f}\n")
                        elif isinstance(valeur, list) and len(valeur) > 0:
                            if all(isinstance(v, (int, float)) for v in valeur[:3]):
                                f.write(f"   {nom} (échantillon): {valeur[:3]}\n")

                # Variations d'entropie
                f.write(f"\n6. VARIATIONS D'ENTROPIE:\n")
                f.write("-" * 25 + "\n")
                variations_count = 0
                for nom, valeur in formules_entropie.items():
                    if 'variation' in nom and isinstance(valeur, (int, float)):
                        f.write(f"   {nom}: {valeur:.6f}\n")
                        variations_count += 1

                f.write(f"\nRÉSUMÉ DES FORMULES D'ENTROPIE:\n")
                f.write("-" * 30 + "\n")
                f.write(f"• Total formules calculées: {len(formules_entropie)}\n")
                f.write(f"• Entropies de Shannon: Mesure du contenu informationnel\n")
                f.write(f"• Entropies de Bernoulli: Patterns binaires S/O optimisés\n")
                f.write(f"• Divergences KL: Distance entre distributions locales/globales\n")
                f.write(f"• Information mutuelle: Dépendance entre métriques\n")
                f.write(f"• Entropies conditionnelles: Incertitude résiduelle H(Y|X)\n")
                f.write(f"• Entropies de Markov: Transitions S→S, S→O, O→S, O→O\n")
                f.write(f"• Qualité du signal: Évaluation prédictive des signaux\n")
                f.write(f"• Force de cohérence: Analyse des ratios L4/L5\n")
                f.write(f"• Formule logarithmique: P(S) = 0.45 + 0.35*log(DIFF+0.01)\n\n")

            # Interprétation des corrélations
            f.write("INTERPRÉTATION DES CORRÉLATIONS:\n")
            f.write("-" * 35 + "\n")

            def interpreter_correlation(r):
                if abs(r) >= 0.8:
                    return "TRÈS FORTE"
                elif abs(r) >= 0.6:
                    return "FORTE"
                elif abs(r) >= 0.4:
                    return "MODÉRÉE"
                elif abs(r) >= 0.2:
                    return "FAIBLE"
                else:
                    return "NÉGLIGEABLE"

            for nom_corr, valeur in correlations.items():
                interpretation = interpreter_correlation(valeur)
                f.write(f"{nom_corr}: {interpretation} ({valeur:.4f})\n")

            # SECTION FORMULES OPÉRATIONNELLES
            formules = correlations_stats.get('formules_operationnelles', {})
            if formules:
                f.write(f"\n\nFORMULES MATHÉMATIQUES OPÉRATIONNELLES\n")
                f.write("=" * 50 + "\n\n")

                # Seuils optimaux
                seuils = formules.get('seuils_optimaux', {})
                if seuils:
                    f.write("SEUILS DE DÉCISION OPTIMAUX:\n")
                    f.write("-" * 30 + "\n")

                    if 'diff' in seuils:
                        f.write("DIFF (Cohérence L4/L5):\n")
                        for niveau, valeur in seuils['diff'].items():
                            f.write(f"  {niveau.upper()}: DIFF <= {valeur:.6f}\n")
                        f.write("\n")

                    if 'ratio_l4' in seuils:
                        f.write("RATIO L4 (Entropie locale/globale):\n")
                        for niveau, valeur in seuils['ratio_l4'].items():
                            f.write(f"  {niveau.upper()}: ratio_l4 >= {valeur:.6f}\n")
                        f.write("\n")

                    if 'ratio_l5' in seuils:
                        f.write("RATIO L5 (Entropie locale/globale):\n")
                        for niveau, valeur in seuils['ratio_l5'].items():
                            f.write(f"  {niveau.upper()}: ratio_l5 >= {valeur:.6f}\n")
                        f.write("\n")

                # Formules de combinaison
                formules_comb = formules.get('formules_combinaison', {})
                if formules_comb:
                    f.write("FORMULES DE CALCUL APPLICABLES:\n")
                    f.write("-" * 35 + "\n")

                    for nom_formule, details in formules_comb.items():
                        f.write(f"{nom_formule.upper()}:\n")
                        f.write(f"  Formule: {details['formule']}\n")
                        f.write(f"  Usage: {details['interpretation']}\n")
                        if 'seuil_decision' in details:
                            f.write(f"  Seuil: >= {details['seuil_decision']}\n")
                        if 'seuil_fiabilite' in details:
                            f.write(f"  Fiabilité: >= {details['seuil_fiabilite']}\n")
                        f.write("\n")

                # Règles par main
                regles = formules.get('regles_par_main', {})
                if regles:
                    f.write("STRATÉGIES PAR POSITION DE MAIN:\n")
                    f.write("-" * 35 + "\n")

                    for periode, details in regles.items():
                        f.write(f"{periode.upper()}:\n")
                        f.write(f"  Condition: {details['condition']}\n")
                        f.write(f"  Stratégie: {details['strategie']}\n")
                        f.write(f"  Formule: {details['formule']}\n\n")

            # TABLEAU EXHAUSTIF DES CORRÉLATIONS ET IMPACT S/O
            tableau_correlations = correlations_stats.get('tableau_correlations_impact', {})
            if tableau_correlations:
                f.write(f"\n\nTABLEAU EXHAUSTIF DES CORRÉLATIONS ET IMPACT S/O\n")
                f.write("=" * 70 + "\n\n")

                impact_so = tableau_correlations.get('impact_so', {})
                if impact_so:
                    f.write("CORRÉLATIONS TRIÉES PAR FORCE PRÉDICTIVE:\n")
                    f.write("-" * 50 + "\n")
                    f.write("CORRÉLATION                           | GLOBALE | S      | O      | DIFF   | FAVORISE | FORCE\n")
                    f.write("-" * 100 + "\n")

                    # Trier par force prédictive
                    correlations_triees = sorted(
                        impact_so.items(),
                        key=lambda x: x[1]['difference_impact'],
                        reverse=True
                    )

                    for nom_corr, donnees_impact in correlations_triees[:30]:  # Top 30
                        nom_court = nom_corr.replace('_avec_', '→')[:35]
                        f.write(f"{nom_court:<37} | {donnees_impact['correlation_globale']:>6.3f} | "
                               f"{donnees_impact['correlation_s']:>6.3f} | {donnees_impact['correlation_o']:>6.3f} | "
                               f"{donnees_impact['difference_impact']:>6.3f} | {donnees_impact['favorise']:>8} | "
                               f"{donnees_impact['force_predictive']}\n")

                    f.write(f"\nLÉGENDE:\n")
                    f.write(f"- GLOBALE: Corrélation sur toutes les données\n")
                    f.write(f"- S: Corrélation spécifique aux cas de continuation\n")
                    f.write(f"- O: Corrélation spécifique aux cas d'alternance\n")
                    f.write(f"- DIFF: |Corr_S - Corr_O| = Force discriminante\n")
                    f.write(f"- FAVORISE: S ou O selon la corrélation la plus forte\n")
                    f.write(f"- FORCE: FORTE (>0.2), MODÉRÉE (>0.1), FAIBLE (≤0.1)\n\n")

                # Métriques disponibles
                metriques = tableau_correlations.get('metriques_disponibles', [])
                if metriques:
                    f.write("MÉTRIQUES ANALYSÉES:\n")
                    f.write("-" * 20 + "\n")
                    for i, metrique in enumerate(metriques, 1):
                        f.write(f"{i:2d}. {metrique}\n")
                    f.write(f"\nTOTAL: {len(metriques)} métriques × {len(metriques)-1}/2 = "
                           f"{len(metriques)*(len(metriques)-1)//2} corrélations calculées\n")

    return nom_fichier

# ============================================================================
# VIII. AFFICHAGE DES RÉSULTATS
# ============================================================================

def afficher_resultats_avec_diff(conditions_s, conditions_o, correlations_stats=None):
    """
    AFFICHAGE DES RÉSULTATS ENRICHI
    ===============================

    Affiche la synthèse des résultats principaux AVEC DIFF ET CORRÉLATIONS.
    Présente les meilleures conditions identifiées et les corrélations principales.

    Args:
        conditions_s (list): Conditions favorisant S
        conditions_o (list): Conditions favorisant O
        correlations_stats (dict): Statistiques et corrélations calculées
    """
    print(f"📊 RÉSULTATS AVEC DIFF:")
    print(f"   Conditions S identifiées: {len(conditions_s)}")
    print(f"   Conditions O identifiées: {len(conditions_o)}")
    
    # Conditions DIFF spécifiques
    conditions_diff_s = [c for c in conditions_s if 'DIFF_' in c['nom']]
    conditions_diff_o = [c for c in conditions_o if 'DIFF_' in c['nom']]
    
    print(f"   Conditions DIFF favorisant S: {len(conditions_diff_s)}")
    print(f"   Conditions DIFF favorisant O: {len(conditions_diff_o)}")
    
    if conditions_diff_s:
        meilleure_diff_s = max(conditions_diff_s, key=lambda x: x['pourcentage_s'])
        print(f"   Meilleure condition DIFF S: {meilleure_diff_s['nom']} ({meilleure_diff_s['pourcentage_s']:.1f}%)")
    
    if conditions_diff_o:
        meilleure_diff_o = max(conditions_diff_o, key=lambda x: x['pourcentage_o'])
        print(f"   Meilleure condition DIFF O: {meilleure_diff_o['nom']} ({meilleure_diff_o['pourcentage_o']:.1f}%)")

    # Affichage des corrélations principales
    if correlations_stats:
        print(f"\n📊 CORRÉLATIONS PRINCIPALES:")
        correlations = correlations_stats.get('correlations', {})
        print(f"   Diff_L4 avec DIFF: {correlations.get('diff_l4_avec_diff', 0):.4f}")
        print(f"   Diff_L5 avec DIFF: {correlations.get('diff_l5_avec_diff', 0):.4f}")
        print(f"   Ratio L4 avec L5: {correlations.get('ratio_l4_avec_l5', 0):.4f}")

        # Corrélation la plus forte
        max_corr = max(correlations.values(), key=abs) if correlations else 0
        max_corr_nom = max(correlations.items(), key=lambda x: abs(x[1]))[0] if correlations else "N/A"
        print(f"   Corrélation la plus forte: {max_corr_nom} ({max_corr:.4f})")

# ============================================================================
# IX. POINT D'ENTRÉE PRINCIPAL
# ============================================================================

# ============================================================================
# CLASSE GÉNÉRIQUE POUR ANALYSE DE MÉTRIQUES ENTROPIQUES
# ============================================================================

class AnalyseurMetriqueGenerique:
    """
    CLASSE GÉNÉRIQUE POUR REPRODUIRE L'ARCHITECTURE DIFF
    ===================================================

    Cette classe permet de reproduire exactement ce qui a été fait avec DIFF
    pour n'importe quelle métrique basée sur les formules d'entropie.

    FONCTIONNALITÉS REPRODUITES :
    - Tranches de qualité personnalisables
    - Combinaisons avec ratios L4/L5
    - Analyse exhaustive des conditions
    - Génération de rapports spécialisés
    - Calculs de corrélations
    - Formules mathématiques dérivées

    MÉTRIQUES SUPPORTÉES (basées sur formules_entropie_python.txt) :
    - DIFF (original) : Entropie de Shannon
    - DIFF_BERNOULLI : Entropie de Bernoulli
    - DIFF_UNIFORM : Entropie uniforme
    - DIFF_JOINT : Entropie jointe
    - DIFF_MARKOV : Entropie de Markov
    - DIFF_METRIC : Entropie métrique
    - DIFF_BERNOULLI_SHIFT : Décalage de Bernoulli
    - DIFF_BSC : Canal binaire symétrique
    - DIFF_ERASURE : Canal effaceur
    - DIFF_HUFFMAN : Efficacité de Huffman
    - DIFF_INVERSE : Métriques inverses
    - DIFF_STD : Écart-type d'entropie
    - DIFF_LOG_PRED : Prédiction logarithmique
    - DIFF_AEP : Équipartition asymptotique
    - DIFF_JENSEN : Inégalité de Jensen
    - DIFF_LOG_SUM : Inégalité log-sum
    - DIFF_CONCAVITY : Concavité d'entropie
    - DIFF_SMB : Shannon-McMillan-Breiman
    - DIFF_ERGODIC : Entropie ergodique
    - DIFF_CHANNEL : Codage de canal
    - DIFF_ERROR : Borne d'erreur
    - DIFF_SPHERE : Borne de sphère
    - DIFF_COMPREHENSIVE : Analyse complète
    - Et toutes les métriques relatives (KL, cross-entropy, conditional, mutual info)

    TOTAL : 25 nouvelles variantes DIFF_X + DIFF original = 26 métriques
    """

    def __init__(self, nom_metrique, fonction_calcul, tranches_qualite=None, formules_derivees=None):
        """
        Initialise l'analyseur pour une métrique spécifique

        Args:
            nom_metrique (str): Nom de la métrique (ex: "SHANNON_ENTROPY", "MUTUAL_INFO")
            fonction_calcul (callable): Fonction pour calculer la métrique à partir des données
            tranches_qualite (list): Liste de tuples (min, max, nom_qualite)
            formules_derivees (dict): Dictionnaire des formules mathématiques dérivées
        """
        self.nom_metrique = nom_metrique
        self.fonction_calcul = fonction_calcul
        self.tranches_qualite = tranches_qualite or self._get_tranches_par_defaut()
        self.formules_derivees = formules_derivees or {}

        # Stockage des résultats
        self.conditions_s = []
        self.conditions_o = []
        self.correlations_stats = {}
        self.donnees_analyse = []

    def _get_tranches_par_defaut(self):
        """Tranches de qualité par défaut (adaptables selon la métrique)"""
        return [
            (0.0, 0.1, "SIGNAL_PARFAIT"),
            (0.1, 0.2, "SIGNAL_EXCELLENT"),
            (0.2, 0.3, "SIGNAL_TRÈS_BON"),
            (0.3, 0.4, "SIGNAL_BON"),
            (0.4, 0.5, "SIGNAL_ACCEPTABLE"),
            (0.5, 0.6, "SIGNAL_RISQUÉ"),
            (0.6, 0.7, "SIGNAL_DOUTEUX"),
            (0.7, 0.8, "SIGNAL_TRÈS_DOUTEUX"),
            (0.8, 10.0, "SIGNAL_INUTILISABLE")
        ]

    def calculer_metrique_sur_donnees(self, donnees):
        """
        Calcule la métrique sur les données d'analyse

        Args:
            donnees (list): Données d'analyse avec ratios L4/L5, patterns, etc.

        Returns:
            list: Valeurs de la métrique calculées
        """
        valeurs_metrique = []

        for donnee in donnees:
            # Calculer la métrique selon la fonction fournie
            valeur_metrique = self.fonction_calcul(donnee)
            valeurs_metrique.append(valeur_metrique)

        return valeurs_metrique

    def enrichir_donnees_avec_metrique(self, donnees):
        """
        Enrichit les données avec la métrique calculée

        Args:
            donnees (list): Données d'analyse avec ratios L4/L5, patterns, etc.

        Returns:
            list: Données enrichies avec la métrique calculée
        """
        donnees_enrichies = []

        for donnee in donnees:
            # Calculer la métrique selon la fonction fournie
            valeur_metrique = self.fonction_calcul(donnee)

            # Enrichir les données
            donnee_enrichie = donnee.copy()
            donnee_enrichie[self.nom_metrique.lower()] = valeur_metrique
            donnees_enrichies.append(donnee_enrichie)

        return donnees_enrichies

    def analyser_toutes_conditions_avec_metrique(self, donnees):
        """
        Analyse exhaustive des conditions avec la métrique (reproduction de analyser_toutes_conditions_avec_diff)

        Args:
            donnees (list): Données d'analyse enrichies avec la métrique

        Returns:
            tuple: (conditions_s, conditions_o)
        """
        print(f"🔬 Analyse exhaustive des conditions AVEC {self.nom_metrique}...")

        conditions_s = []
        conditions_o = []

        # ANALYSE 1: Métrique pure par tranches de qualité
        print(f"   📊 Analyse {self.nom_metrique} par tranches de qualité...")

        for min_val, max_val, nom in self.tranches_qualite:
            donnees_tranche = [d for d in donnees
                             if min_val <= d[self.nom_metrique.lower()] < max_val]
            if len(donnees_tranche) >= 100:
                self._analyser_tranche(donnees_tranche, f"{self.nom_metrique}_{nom}",
                                     conditions_s, conditions_o)

        # ANALYSE 2: Ratios L4 par tranches (reproduction exacte)
        print("   📊 Analyse ratios L4...")
        tranches_l4 = [
            (0.0, 0.3, "ORDRE_TRÈS_FORT"),
            (0.3, 0.5, "ORDRE_FORT"),
            (0.5, 0.7, "ORDRE_MODÉRÉ"),
            (0.7, 0.9, "ÉQUILIBRE"),
            (0.9, 1.0, "CHAOS")
        ]

        for min_val, max_val, nom in tranches_l4:
            donnees_tranche = [d for d in donnees if min_val <= d['ratio_l4'] < max_val]
            if len(donnees_tranche) >= 100:
                self._analyser_tranche(donnees_tranche, f"L4_{nom}", conditions_s, conditions_o)

        # ANALYSE 3: Ratios L5 par tranches (reproduction exacte)
        print("   📊 Analyse ratios L5...")
        tranches_l5 = tranches_l4  # Mêmes tranches

        for min_val, max_val, nom in tranches_l5:
            donnees_tranche = [d for d in donnees if min_val <= d['ratio_l5'] < max_val]
            if len(donnees_tranche) >= 100:
                self._analyser_tranche(donnees_tranche, f"L5_{nom}", conditions_s, conditions_o)

        # ANALYSE 4: Combinaisons MÉTRIQUE + Ratios (adaptation de DIFF)
        print(f"   📊 Analyse combinaisons {self.nom_metrique} + Ratios...")
        combinaisons_metrique = self._generer_combinaisons_metrique()

        for nom, condition in combinaisons_metrique.items():
            donnees_cond = [d for d in donnees if condition(d)]
            if len(donnees_cond) >= 100:
                self._analyser_tranche(donnees_cond, f"COMB_{nom}", conditions_s, conditions_o)

        print(f"✅ Analyse AVEC {self.nom_metrique} terminée: {len(conditions_s)} conditions S, {len(conditions_o)} conditions O")

        self.conditions_s = conditions_s
        self.conditions_o = conditions_o
        return conditions_s, conditions_o

    def _generer_combinaisons_metrique(self):
        """
        Génère les combinaisons métrique + ratios (adaptation des combinaisons DIFF)

        Returns:
            dict: Dictionnaire des conditions de combinaison
        """
        nom_metrique_lower = self.nom_metrique.lower()

        # Seuils adaptatifs basés sur les tranches de qualité
        seuil_parfait = self.tranches_qualite[0][1]  # Fin de la première tranche
        seuil_excellent = self.tranches_qualite[1][1]  # Fin de la deuxième tranche
        seuil_douteux = self.tranches_qualite[-3][0]  # Début de l'avant-dernière tranche

        combinaisons = {
            f"ORDRE_FORT_{self.nom_metrique}_PARFAIT":
                lambda d: d['ratio_l4'] < 0.5 and d[nom_metrique_lower] < seuil_parfait,
            f"ORDRE_FORT_{self.nom_metrique}_EXCELLENT":
                lambda d: d['ratio_l4'] < 0.5 and seuil_parfait <= d[nom_metrique_lower] < seuil_excellent,
            f"ORDRE_FORT_{self.nom_metrique}_DOUTEUX":
                lambda d: d['ratio_l4'] < 0.5 and d[nom_metrique_lower] > seuil_douteux,

            f"ORDRE_MODÉRÉ_{self.nom_metrique}_PARFAIT":
                lambda d: 0.5 <= d['ratio_l4'] < 0.7 and d[nom_metrique_lower] < seuil_parfait,
            f"ORDRE_MODÉRÉ_{self.nom_metrique}_EXCELLENT":
                lambda d: 0.5 <= d['ratio_l4'] < 0.7 and seuil_parfait <= d[nom_metrique_lower] < seuil_excellent,
            f"ORDRE_MODÉRÉ_{self.nom_metrique}_DOUTEUX":
                lambda d: 0.5 <= d['ratio_l4'] < 0.7 and d[nom_metrique_lower] > seuil_douteux,

            f"ÉQUILIBRE_{self.nom_metrique}_PARFAIT":
                lambda d: 0.7 <= d['ratio_l4'] <= 0.9 and d[nom_metrique_lower] < seuil_parfait,
            f"ÉQUILIBRE_{self.nom_metrique}_EXCELLENT":
                lambda d: 0.7 <= d['ratio_l4'] <= 0.9 and seuil_parfait <= d[nom_metrique_lower] < seuil_excellent,
            f"ÉQUILIBRE_{self.nom_metrique}_DOUTEUX":
                lambda d: 0.7 <= d['ratio_l4'] <= 0.9 and d[nom_metrique_lower] > seuil_douteux,

            f"CHAOS_{self.nom_metrique}_PARFAIT":
                lambda d: d['ratio_l4'] > 0.9 and d[nom_metrique_lower] < seuil_parfait,
            f"CHAOS_{self.nom_metrique}_EXCELLENT":
                lambda d: d['ratio_l4'] > 0.9 and seuil_parfait <= d[nom_metrique_lower] < seuil_excellent,
            f"CHAOS_{self.nom_metrique}_DOUTEUX":
                lambda d: d['ratio_l4'] > 0.9 and d[nom_metrique_lower] > seuil_douteux,

            # Combinaisons avec variations (si disponibles)
            f"VARIATIONS_FORTES_{self.nom_metrique}_PARFAIT":
                lambda d: (d.get('diff_l4', 0) > 0.1 or d.get('diff_l5', 0) > 0.1) and d[nom_metrique_lower] < seuil_parfait,
            f"VARIATIONS_FORTES_{self.nom_metrique}_EXCELLENT":
                lambda d: (d.get('diff_l4', 0) > 0.1 or d.get('diff_l5', 0) > 0.1) and seuil_parfait <= d[nom_metrique_lower] < seuil_excellent,
            f"VARIATIONS_FORTES_{self.nom_metrique}_DOUTEUX":
                lambda d: (d.get('diff_l4', 0) > 0.1 or d.get('diff_l5', 0) > 0.1) and d[nom_metrique_lower] > seuil_douteux,

            f"STABILITÉ_{self.nom_metrique}_PARFAIT":
                lambda d: d.get('diff_l4', 0) < 0.02 and d.get('diff_l5', 0) < 0.02 and d[nom_metrique_lower] < seuil_parfait,
            f"STABILITÉ_{self.nom_metrique}_EXCELLENT":
                lambda d: d.get('diff_l4', 0) < 0.02 and d.get('diff_l5', 0) < 0.02 and seuil_parfait <= d[nom_metrique_lower] < seuil_excellent
        }

        return combinaisons

    def _analyser_tranche(self, donnees_tranche, nom_condition, conditions_s, conditions_o):
        """
        Analyse une tranche de données (reproduction exacte de analyser_tranche)

        Args:
            donnees_tranche (list): Données de la tranche
            nom_condition (str): Nom de la condition
            conditions_s (list): Liste des conditions S
            conditions_o (list): Liste des conditions O
        """
        if len(donnees_tranche) < 100:
            return

        # Compter S et O
        count_s = sum(1 for d in donnees_tranche if d['pattern'] == 'S')
        count_o = sum(1 for d in donnees_tranche if d['pattern'] == 'O')
        total = count_s + count_o

        if total == 0:
            return

        pourcentage_s = (count_s / total) * 100
        pourcentage_o = (count_o / total) * 100

        # Seuils de classification (reproduction exacte)
        if pourcentage_s >= 55.0:  # Favorise S
            force = "FORTE" if pourcentage_s >= 60.0 else "MODÉRÉE" if pourcentage_s >= 57.0 else "FAIBLE"
            conditions_s.append({
                'nom': nom_condition,
                'pourcentage_s': pourcentage_s,
                'pourcentage_o': pourcentage_o,
                'total_cas': total,
                'force': force
            })
        elif pourcentage_o >= 55.0:  # Favorise O
            force = "FORTE" if pourcentage_o >= 60.0 else "MODÉRÉE" if pourcentage_o >= 57.0 else "FAIBLE"
            conditions_o.append({
                'nom': nom_condition,
                'pourcentage_s': pourcentage_s,
                'pourcentage_o': pourcentage_o,
                'total_cas': total,
                'force': force
            })

    def calculer_correlations_essentielles_metrique(self, donnees):
        """
        Calcule les corrélations essentielles avec la métrique (adaptation de calculer_correlations_essentielles)

        Args:
            donnees (list): Données d'analyse avec la métrique

        Returns:
            dict: Statistiques et corrélations
        """
        print(f"🔗 Calcul des corrélations essentielles avec {self.nom_metrique}...")

        # Extraction des variables
        nom_metrique_lower = self.nom_metrique.lower()
        metrique_values = [d[nom_metrique_lower] for d in donnees]
        diff_l4_values = [d.get('diff_l4', 0) for d in donnees]
        diff_l5_values = [d.get('diff_l5', 0) for d in donnees]
        ratio_l4_values = [d['ratio_l4'] for d in donnees]
        ratio_l5_values = [d['ratio_l5'] for d in donnees]

        def calculer_correlation(x_values, y_values):
            """Calcule le coefficient de corrélation de Pearson"""
            if len(x_values) != len(y_values) or len(x_values) < 2:
                return 0.0

            # Moyennes
            mean_x = sum(x_values) / len(x_values)
            mean_y = sum(y_values) / len(y_values)

            # Numérateur et dénominateurs
            numerator = sum((x - mean_x) * (y - mean_y) for x, y in zip(x_values, y_values))
            sum_sq_x = sum((x - mean_x) ** 2 for x in x_values)
            sum_sq_y = sum((y - mean_y) ** 2 for y in y_values)

            denominator = (sum_sq_x * sum_sq_y) ** 0.5

            if denominator == 0:
                return 0.0

            return numerator / denominator

        # Calcul des corrélations essentielles avec la métrique
        correlations = {
            f'diff_l4_avec_{nom_metrique_lower}': calculer_correlation(diff_l4_values, metrique_values),
            f'diff_l5_avec_{nom_metrique_lower}': calculer_correlation(diff_l5_values, metrique_values),
            f'ratio_l4_avec_{nom_metrique_lower}': calculer_correlation(ratio_l4_values, metrique_values),
            f'ratio_l5_avec_{nom_metrique_lower}': calculer_correlation(ratio_l5_values, metrique_values),
            'ratio_l4_avec_l5': calculer_correlation(ratio_l4_values, ratio_l5_values),
            'diff_l4_avec_l5': calculer_correlation(diff_l4_values, diff_l5_values)
        }

        print(f"   ✅ {len(correlations)} corrélations essentielles calculées avec {self.nom_metrique}")

        # Statistiques descriptives
        def calculer_statistiques_descriptives(values):
            if not values:
                return {'mean': 0, 'median': 0, 'std_dev': 0, 'min': 0, 'max': 0, 'count': 0}

            sorted_values = sorted(values)
            n = len(values)
            mean = sum(values) / n
            median = sorted_values[n//2] if n % 2 == 1 else (sorted_values[n//2-1] + sorted_values[n//2]) / 2
            variance = sum((x - mean) ** 2 for x in values) / n
            std_dev = variance ** 0.5

            return {
                'mean': mean,
                'median': median,
                'std_dev': std_dev,
                'min': min(values),
                'max': max(values),
                'count': n
            }

        statistiques = {
            'diff_l4': calculer_statistiques_descriptives(diff_l4_values),
            'diff_l5': calculer_statistiques_descriptives(diff_l5_values),
            nom_metrique_lower: calculer_statistiques_descriptives(metrique_values),
            'ratio_l4': calculer_statistiques_descriptives(ratio_l4_values),
            'ratio_l5': calculer_statistiques_descriptives(ratio_l5_values)
        }

        self.correlations_stats = {
            'correlations': correlations,
            'statistiques': statistiques,
            'total_observations': len(donnees),
            'metrique_analysee': self.nom_metrique
        }

        return self.correlations_stats

    def analyser_tranches_qualite(self, donnees, valeurs_metrique):
        """
        Analyse les tranches de qualité pour la métrique

        Args:
            donnees: Données d'analyse
            valeurs_metrique: Valeurs de la métrique calculées

        Returns:
            list: Résultats d'analyse par tranche
        """
        resultats_tranches = []

        for min_val, max_val, nom_qualite in self.tranches_qualite:
            # Compter les occurrences dans cette tranche
            count_tranche = sum(1 for val in valeurs_metrique if min_val <= val < max_val)

            if count_tranche > 0:
                # Analyser les patterns S/O dans cette tranche
                patterns_s = sum(1 for i, val in enumerate(valeurs_metrique)
                               if min_val <= val < max_val and donnees[i].get('pattern') == 1)
                patterns_o = count_tranche - patterns_s

                pourcentage_s = (patterns_s / count_tranche) * 100 if count_tranche > 0 else 0

                resultats_tranches.append({
                    'tranche': nom_qualite,
                    'min': min_val,
                    'max': max_val,
                    'count': count_tranche,
                    'patterns_s': patterns_s,
                    'patterns_o': patterns_o,
                    'pourcentage_s': pourcentage_s
                })

        return resultats_tranches

    def analyser_correlations_patterns(self, donnees, valeurs_metrique):
        """
        Analyse les corrélations entre la métrique et les patterns

        Args:
            donnees: Données d'analyse
            valeurs_metrique: Valeurs de la métrique calculées

        Returns:
            dict: Corrélations calculées
        """
        patterns = [d.get('pattern', 0) for d in donnees]

        def calculer_correlation(x_values, y_values):
            """Calcule le coefficient de corrélation de Pearson"""
            if len(x_values) != len(y_values) or len(x_values) < 2:
                return 0.0

            # Moyennes
            mean_x = sum(x_values) / len(x_values)
            mean_y = sum(y_values) / len(y_values)

            # Numérateur et dénominateurs
            numerator = sum((x - mean_x) * (y - mean_y) for x, y in zip(x_values, y_values))
            sum_sq_x = sum((x - mean_x) ** 2 for x in x_values)
            sum_sq_y = sum((y - mean_y) ** 2 for y in y_values)

            denominator = (sum_sq_x * sum_sq_y) ** 0.5

            if denominator == 0:
                return 0.0

            return numerator / denominator

        correlation_metrique_pattern = calculer_correlation(valeurs_metrique, patterns)

        return {
            'correlation_metrique_pattern': correlation_metrique_pattern,
            'moyenne_metrique': sum(valeurs_metrique) / len(valeurs_metrique) if valeurs_metrique else 0,
            'ecart_type_metrique': (sum((x - sum(valeurs_metrique) / len(valeurs_metrique))**2 for x in valeurs_metrique) / len(valeurs_metrique))**0.5 if valeurs_metrique else 0
        }

    def generer_tableau_predictif_avec_metrique(self, total_donnees):
        """
        Génère le tableau prédictif avec la métrique (adaptation de generer_tableau_predictif_avec_diff)

        Args:
            total_donnees (int): Nombre total de données analysées

        Returns:
            str: Nom du fichier généré
        """
        from datetime import datetime

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        nom_fichier = f"tableau_predictif_avec_{self.nom_metrique.lower()}_{timestamp}.txt"

        with open(nom_fichier, 'w', encoding='utf-8') as f:
            f.write(f"TABLEAU PRÉDICTIF EXHAUSTIF S/O AVEC {self.nom_metrique}\n")
            f.write("=" * 70 + "\n\n")
            f.write(f"MÉTRIQUE ANALYSÉE : {self.nom_metrique}\n")
            f.write(f"Architecture reproduite depuis DIFF\n\n")
            f.write(f"Date de génération: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Données analysées: {total_donnees:,} points\n")
            f.write(f"Conditions S identifiées: {len(self.conditions_s)}\n")
            f.write(f"Conditions O identifiées: {len(self.conditions_o)}\n\n")

            # Signification des tranches de qualité
            f.write(f"TRANCHES DE QUALITÉ {self.nom_metrique}:\n")
            for min_val, max_val, nom in self.tranches_qualite:
                f.write(f"- {min_val:.3f} ≤ {self.nom_metrique} < {max_val:.3f} : {nom}\n")
            f.write("\n")

            # Conditions S
            f.write("CONDITIONS QUI FAVORISENT S (CONTINUATION)\n")
            f.write("=" * 50 + "\n\n")

            conditions_s_triees = sorted(self.conditions_s, key=lambda x: x['pourcentage_s'], reverse=True)
            for i, cond in enumerate(conditions_s_triees, 1):
                f.write(f"{i:2d}. {cond['nom']:<40} | {cond['pourcentage_s']:5.1f}% S | "
                       f"{cond['total_cas']:6,} cas | {cond['force']}\n")

            f.write(f"\nTOTAL CONDITIONS S: {len(conditions_s_triees)}\n\n")

            # Conditions O
            f.write("CONDITIONS QUI FAVORISENT O (ALTERNANCE)\n")
            f.write("=" * 50 + "\n\n")

            conditions_o_triees = sorted(self.conditions_o, key=lambda x: x['pourcentage_o'], reverse=True)
            for i, cond in enumerate(conditions_o_triees, 1):
                f.write(f"{i:2d}. {cond['nom']:<40} | {cond['pourcentage_o']:5.1f}% O | "
                       f"{cond['total_cas']:6,} cas | {cond['force']}\n")

            f.write(f"\nTOTAL CONDITIONS O: {len(conditions_o_triees)}\n\n")

            # Analyse spéciale métrique
            f.write(f"ANALYSE SPÉCIALE CONDITIONS {self.nom_metrique}\n")
            f.write("=" * 40 + "\n\n")

            conditions_metrique_s = [c for c in self.conditions_s if self.nom_metrique in c['nom']]
            conditions_metrique_o = [c for c in self.conditions_o if self.nom_metrique in c['nom']]

            f.write(f"CONDITIONS {self.nom_metrique} FAVORISANT S:\n")
            for cond in sorted(conditions_metrique_s, key=lambda x: x['pourcentage_s'], reverse=True):
                f.write(f"  {cond['nom']}: {cond['pourcentage_s']:.1f}% S ({cond['total_cas']:,} cas)\n")

            f.write(f"\nCONDITIONS {self.nom_metrique} FAVORISANT O:\n")
            for cond in sorted(conditions_metrique_o, key=lambda x: x['pourcentage_o'], reverse=True):
                f.write(f"  {cond['nom']}: {cond['pourcentage_o']:.1f}% O ({cond['total_cas']:,} cas)\n")

            # Section corrélations
            if self.correlations_stats:
                f.write(f"\n\nANALYSES STATISTIQUES ET CORRÉLATIONS AVEC {self.nom_metrique}\n")
                f.write("=" * 60 + "\n\n")

                correlations = self.correlations_stats.get('correlations', {})
                statistiques = self.correlations_stats.get('statistiques', {})

                f.write("CORRÉLATIONS PRINCIPALES:\n")
                f.write("-" * 30 + "\n")
                for nom_corr, valeur in correlations.items():
                    f.write(f"{nom_corr:<30} : {valeur:>7.4f}\n")
                f.write("\n")

                # Statistiques descriptives
                f.write("STATISTIQUES DESCRIPTIVES:\n")
                f.write("-" * 30 + "\n")

                for var, stats in statistiques.items():
                    if stats:
                        f.write(f"{var.upper()}:\n")
                        f.write(f"  Moyenne    : {stats['mean']:>10.6f}\n")
                        f.write(f"  Médiane    : {stats['median']:>10.6f}\n")
                        f.write(f"  Écart-type : {stats['std_dev']:>10.6f}\n")
                        f.write(f"  Min/Max    : {stats['min']:>10.6f} / {stats['max']:>10.6f}\n")
                        f.write(f"  Observations: {stats['count']:>9,}\n\n")

            # Formules dérivées si disponibles
            if self.formules_derivees:
                f.write(f"FORMULES MATHÉMATIQUES DÉRIVÉES POUR {self.nom_metrique}\n")
                f.write("=" * 50 + "\n\n")

                for nom_formule, formule in self.formules_derivees.items():
                    f.write(f"{nom_formule}:\n")
                    f.write(f"  {formule}\n\n")

            f.write(f"\n{'='*70}\n")
            f.write(f"RAPPORT GÉNÉRÉ PAR AnalyseurMetriqueGenerique\n")
            f.write(f"Architecture reproduite depuis l'analyse DIFF\n")
            f.write(f"Métrique analysée: {self.nom_metrique}\n")
            f.write(f"{'='*70}\n")

        return nom_fichier

    def afficher_resultats_avec_metrique(self):
        """
        Affiche les résultats avec la métrique (adaptation de afficher_resultats_avec_diff)
        """
        print(f"📊 RÉSULTATS AVEC {self.nom_metrique}:")
        print(f"   Conditions S identifiées: {len(self.conditions_s)}")
        print(f"   Conditions O identifiées: {len(self.conditions_o)}")

        # Conditions métrique spécifiques
        conditions_metrique_s = [c for c in self.conditions_s if self.nom_metrique in c['nom']]
        conditions_metrique_o = [c for c in self.conditions_o if self.nom_metrique in c['nom']]

        print(f"   Conditions {self.nom_metrique} favorisant S: {len(conditions_metrique_s)}")
        print(f"   Conditions {self.nom_metrique} favorisant O: {len(conditions_metrique_o)}")

        if conditions_metrique_s:
            meilleure_metrique_s = max(conditions_metrique_s, key=lambda x: x['pourcentage_s'])
            print(f"   Meilleure condition {self.nom_metrique} S: {meilleure_metrique_s['nom']} ({meilleure_metrique_s['pourcentage_s']:.1f}%)")

        if conditions_metrique_o:
            meilleure_metrique_o = max(conditions_metrique_o, key=lambda x: x['pourcentage_o'])
            print(f"   Meilleure condition {self.nom_metrique} O: {meilleure_metrique_o['nom']} ({meilleure_metrique_o['pourcentage_o']:.1f}%)")

        # Affichage des corrélations principales
        if self.correlations_stats:
            print(f"\n📊 CORRÉLATIONS PRINCIPALES AVEC {self.nom_metrique}:")
            correlations = self.correlations_stats.get('correlations', {})

            # Afficher les 3 corrélations les plus fortes
            correlations_triees = sorted(correlations.items(), key=lambda x: abs(x[1]), reverse=True)
            for nom, valeur in correlations_triees[:3]:
                print(f"   {nom}: {valeur:.4f}")

            # Corrélation la plus forte
            if correlations_triees:
                max_corr_nom, max_corr_val = correlations_triees[0]
                print(f"   Corrélation la plus forte: {max_corr_nom} ({max_corr_val:.4f})")


# ============================================================================
# FONCTIONS D'AIDE POUR CRÉER DES MÉTRIQUES BASÉES SUR LES FORMULES D'ENTROPIE
# ============================================================================

def creer_fonction_shannon_entropy():
    """
    Crée une fonction de calcul d'entropie de Shannon pour l'analyseur générique

    Returns:
        callable: Fonction qui calcule l'entropie de Shannon à partir des données
    """
    import math

    def calculer_shannon_entropy(donnee):
        """
        Calcule l'entropie de Shannon H(p) = -∑ p(x) log₂(p(x))
        basée sur les ratios L4 et L5
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Créer une distribution à partir des ratios
        # Normaliser pour que la somme = 1
        total = ratio_l4 + ratio_l5
        if total == 0:
            return 0.0

        p1 = ratio_l4 / total
        p2 = ratio_l5 / total

        # Calculer l'entropie de Shannon
        entropy = 0.0
        for p in [p1, p2]:
            if p > 0:
                entropy -= p * math.log2(p)

        return entropy

    return calculer_shannon_entropy

def creer_fonction_bernoulli_entropy():
    """
    Crée une fonction de calcul d'entropie de Bernoulli pour l'analyseur générique

    Returns:
        callable: Fonction qui calcule l'entropie de Bernoulli
    """
    import math

    def calculer_bernoulli_entropy(donnee):
        """
        Calcule l'entropie de Bernoulli h(a) = -a log₂(a) - (1-a) log₂(1-a)
        basée sur le ratio L4 comme paramètre de Bernoulli
        """
        a = donnee.get('ratio_l4', 0.5)

        # Assurer que a est dans [0,1]
        a = max(0.0, min(1.0, a))

        if a <= 0 or a >= 1:
            return 0.0  # Cas déterministes

        return -a * math.log2(a) - (1 - a) * math.log2(1 - a)

    return calculer_bernoulli_entropy

def creer_fonction_mutual_information():
    """
    Crée une fonction de calcul d'information mutuelle pour l'analyseur générique

    Returns:
        callable: Fonction qui calcule l'information mutuelle
    """
    import math

    def calculer_mutual_information(donnee):
        """
        Calcule l'information mutuelle I(X;Y) approximée
        basée sur la corrélation entre L4 et L5
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Approximation simple de l'information mutuelle
        # basée sur la différence absolue (plus la différence est grande, moins il y a de dépendance)
        diff_abs = abs(ratio_l4 - ratio_l5)

        # Transformer en information mutuelle (0 = indépendant, 1 = totalement dépendant)
        mutual_info = 1.0 - diff_abs

        return max(0.0, mutual_info)

    return calculer_mutual_information

def creer_fonction_relative_entropy():
    """
    Crée une fonction de calcul d'entropie relative (divergence KL) pour l'analyseur générique

    Returns:
        callable: Fonction qui calcule l'entropie relative
    """
    import math

    def calculer_relative_entropy(donnee):
        """
        Calcule l'entropie relative D(p||q) = ∑ p(x) log₂(p(x)/q(x))
        où p est basé sur L4 et q sur L5
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Créer deux distributions
        p = [ratio_l4, 1 - ratio_l4]
        q = [ratio_l5, 1 - ratio_l5]

        # Calculer la divergence KL
        divergence = 0.0
        for i in range(len(p)):
            if p[i] > 0:
                if q[i] > 0:
                    divergence += p[i] * math.log2(p[i] / q[i])
                else:
                    return float('inf')  # q(x)=0 mais p(x)>0

        return max(0.0, divergence)

    return calculer_relative_entropy

def creer_fonction_cross_entropy():
    """
    Crée une fonction de calcul d'entropie croisée pour l'analyseur générique

    Returns:
        callable: Fonction qui calcule l'entropie croisée
    """
    import math

    def calculer_cross_entropy(donnee):
        """
        Calcule l'entropie croisée H(p,q) = -∑ p(x) log₂(q(x))
        où p est basé sur L4 et q sur L5
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Créer deux distributions
        p = [ratio_l4, 1 - ratio_l4]
        q = [ratio_l5, 1 - ratio_l5]

        # Calculer l'entropie croisée
        cross_ent = 0.0
        for i in range(len(p)):
            if p[i] > 0:
                if q[i] > 0:
                    cross_ent -= p[i] * math.log2(q[i])
                else:
                    return float('inf')  # q(x)=0 mais p(x)>0

        return max(0.0, cross_ent)

    return calculer_cross_entropy

def creer_fonction_conditional_entropy():
    """
    Crée une fonction de calcul d'entropie conditionnelle pour l'analyseur générique

    Returns:
        callable: Fonction qui calcule l'entropie conditionnelle
    """
    import math

    def calculer_conditional_entropy(donnee):
        """
        Calcule l'entropie conditionnelle H(Y|X) approximée
        basée sur l'incertitude de L5 sachant L4
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Approximation : plus L4 et L5 sont corrélés, moins l'entropie conditionnelle est élevée
        correlation_strength = 1.0 - abs(ratio_l4 - ratio_l5)

        # Entropie de base de L5
        if ratio_l5 <= 0 or ratio_l5 >= 1:
            base_entropy = 0.0
        else:
            base_entropy = -ratio_l5 * math.log2(ratio_l5) - (1 - ratio_l5) * math.log2(1 - ratio_l5)

        # Réduire l'entropie selon la corrélation
        conditional_entropy = base_entropy * (1.0 - correlation_strength)

        return max(0.0, conditional_entropy)

    return calculer_conditional_entropy

# ============================================================================
# NOUVELLES MÉTRIQUES DIFF_X : 25 VARIANTES BASÉES SUR FORMULES D'ENTROPIE
# ============================================================================

def creer_fonction_bernoulli_entropy():
    """
    DIFF_BERNOULLI : Remplace Shannon par l'entropie de Bernoulli
    """
    import math

    def calculer_bernoulli_entropy(donnee):
        """
        Calcule l'entropie de Bernoulli h(a) = -a log₂(a) - (1-a) log₂(1-a)
        basée sur les ratios L4 et L5
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Utiliser la moyenne des ratios comme paramètre de Bernoulli
        a = (ratio_l4 + ratio_l5) / 2.0

        # Protection contre les cas limites
        if a <= 0.001 or a >= 0.999:
            return 0.0

        # Calcul de l'entropie de Bernoulli
        return -a * math.log2(a) - (1 - a) * math.log2(1 - a)

    return calculer_bernoulli_entropy

def creer_fonction_uniform_entropy():
    """
    DIFF_UNIFORM : Remplace Shannon par l'entropie uniforme
    """
    import math

    def calculer_uniform_entropy(donnee):
        """
        Calcule l'entropie uniforme H(uniform) = log₂(n)
        basée sur la diversité des ratios L4 et L5
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Estimer le nombre d'éléments équiprobables basé sur les ratios
        # Plus les ratios sont différents, plus la diversité est élevée
        diversity_factor = 1.0 + abs(ratio_l4 - ratio_l5) * 17  # 18 valeurs INDEX5 max
        n = max(1, int(diversity_factor))

        return math.log2(n)

    return calculer_uniform_entropy

def creer_fonction_joint_entropy():
    """
    DIFF_JOINT : Remplace Shannon par l'entropie jointe
    """
    import math

    def calculer_joint_entropy(donnee):
        """
        Calcule l'entropie jointe H(X,Y) approximée
        basée sur la distribution jointe des ratios L4 et L5
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Créer une distribution jointe approximative
        total = ratio_l4 + ratio_l5
        if total == 0:
            return 0.0

        # Distribution jointe simplifiée (2x2)
        p11 = (ratio_l4 * ratio_l5) / (total * total)
        p10 = (ratio_l4 * (1 - ratio_l5)) / (total * total)
        p01 = ((1 - ratio_l4) * ratio_l5) / (total * total)
        p00 = ((1 - ratio_l4) * (1 - ratio_l5)) / (total * total)

        # Normaliser
        probs = [p11, p10, p01, p00]
        prob_sum = sum(probs)
        if prob_sum > 0:
            probs = [p / prob_sum for p in probs]

        # Calculer l'entropie jointe
        entropy = 0.0
        for p in probs:
            if p > 0:
                entropy -= p * math.log2(p)

        return entropy

    return calculer_joint_entropy

def creer_fonction_markov_entropy():
    """
    DIFF_MARKOV : Remplace Shannon par l'entropie de Markov
    """
    import math

    def calculer_markov_entropy(donnee):
        """
        Calcule l'entropie de Markov H(Ξ) = -∑ μ(x) P_{xy} log₂(P_{xy})
        approximée basée sur les transitions L4→L5
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Distribution stationnaire approximative
        mu = [ratio_l4, 1 - ratio_l4]

        # Matrice de transition approximative basée sur L4→L5
        transition_strength = abs(ratio_l5 - ratio_l4)
        p11 = 1.0 - transition_strength  # Probabilité de rester dans le même état
        p12 = transition_strength        # Probabilité de changer d'état
        p21 = transition_strength
        p22 = 1.0 - transition_strength

        P = [[p11, p12], [p21, p22]]

        # Calculer l'entropie de Markov
        entropy = 0.0
        for i in range(2):
            for j in range(2):
                if mu[i] > 0 and P[i][j] > 0:
                    entropy -= mu[i] * P[i][j] * math.log2(P[i][j])

        return entropy

    return calculer_markov_entropy

def creer_fonction_metric_entropy():
    """
    DIFF_METRIC : Remplace Shannon par l'entropie métrique
    """
    import math

    def calculer_metric_entropy(donnee):
        """
        Calcule l'entropie métrique h_μ(T) approximée
        basée sur la limite des entropies conditionnelles L4 et L5
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Approximation de la limite métrique
        # Plus les ratios convergent, plus l'entropie métrique est stable
        convergence = 1.0 - abs(ratio_l4 - ratio_l5)
        base_entropy = (ratio_l4 + ratio_l5) / 2.0

        return base_entropy * convergence

    return calculer_metric_entropy

def creer_fonction_bernoulli_shift_entropy():
    """
    DIFF_BERNOULLI_SHIFT : Remplace Shannon par l'entropie du décalage de Bernoulli
    """
    import math

    def calculer_bernoulli_shift_entropy(donnee):
        """
        Calcule l'entropie du décalage de Bernoulli B(p)
        h_μ(T) = H(p) pour décalage de Bernoulli
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Paramètre de Bernoulli basé sur la moyenne des ratios
        p = (ratio_l4 + ratio_l5) / 2.0

        # Protection contre les cas limites
        if p <= 0.001 or p >= 0.999:
            return 0.0

        # Entropie de Bernoulli = entropie du décalage
        return -p * math.log2(p) - (1 - p) * math.log2(1 - p)

    return calculer_bernoulli_shift_entropy

def creer_fonction_binary_symmetric_channel():
    """
    DIFF_BSC : Remplace Shannon par la capacité du canal binaire symétrique
    """
    import math

    def calculer_bsc_capacity(donnee):
        """
        Calcule la capacité du canal binaire symétrique κ = 1 - h(α)
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Probabilité d'erreur basée sur la différence des ratios
        error_prob = min(0.5, abs(ratio_l4 - ratio_l5))

        # Protection contre les cas limites
        if error_prob <= 0.001 or error_prob >= 0.499:
            return 1.0 if error_prob <= 0.001 else 0.0

        # Capacité = 1 - h(α)
        h_alpha = -error_prob * math.log2(error_prob) - (1 - error_prob) * math.log2(1 - error_prob)
        return max(0.0, 1.0 - h_alpha)

    return calculer_bsc_capacity

def creer_fonction_erasure_channel():
    """
    DIFF_ERASURE : Remplace Shannon par la capacité du canal effaceur
    """
    def calculer_erasure_capacity(donnee):
        """
        Calcule la capacité du canal effaceur κ = 1 - α
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Probabilité d'effacement basée sur l'incertitude des ratios
        erasure_prob = min(1.0, abs(ratio_l4 - ratio_l5) * 2.0)

        return max(0.0, 1.0 - erasure_prob)

    return calculer_erasure_capacity

def creer_fonction_huffman_efficiency():
    """
    DIFF_HUFFMAN : Remplace Shannon par l'efficacité de Huffman
    """
    import math

    def calculer_huffman_efficiency(donnee):
        """
        Calcule l'efficacité d'un code par rapport à la borne de Shannon
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Distribution approximative
        p1 = ratio_l4 / (ratio_l4 + ratio_l5) if (ratio_l4 + ratio_l5) > 0 else 0.5
        p2 = 1 - p1

        # Entropie théorique
        if p1 <= 0 or p1 >= 1:
            theoretical_entropy = 0.0
        else:
            theoretical_entropy = -p1 * math.log2(p1) - p2 * math.log2(p2)

        # Longueur moyenne approximative (codes optimaux)
        if p1 == 0 or p2 == 0:
            avg_length = 1.0
        else:
            avg_length = 1.0  # Code binaire optimal pour 2 symboles

        # Efficacité = H(p) / L_avg
        if avg_length > 0:
            return theoretical_entropy / avg_length
        else:
            return 0.0

    return calculer_huffman_efficiency

def creer_fonction_inverse_entropy():
    """
    DIFF_INVERSE : Remplace Shannon par les métriques d'entropie inverse
    """
    import math

    def calculer_inverse_entropy(donnee):
        """
        Calcule les métriques d'entropie inverse (ordre vs chaos)
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Entropie de base
        avg_ratio = (ratio_l4 + ratio_l5) / 2.0
        if avg_ratio <= 0.001 or avg_ratio >= 0.999:
            base_entropy = 0.001
        else:
            base_entropy = -avg_ratio * math.log2(avg_ratio) - (1 - avg_ratio) * math.log2(1 - avg_ratio)

        # Métrique inverse (ordre)
        if base_entropy > 0:
            return 1.0 / base_entropy
        else:
            return 1000.0  # Ordre maximal

    return calculer_inverse_entropy

def creer_fonction_standard_deviation_entropy():
    """
    DIFF_STD : Remplace Shannon par l'écart-type d'entropie
    """
    import math

    def calculer_std_entropy(donnee):
        """
        Calcule l'écart-type d'une séquence d'entropies approximée
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Séquence approximative d'entropies [L4, L5]
        entropy_sequence = [ratio_l4, ratio_l5]

        if len(entropy_sequence) < 2:
            return 0.0

        # Calcul de l'écart-type
        mean = sum(entropy_sequence) / len(entropy_sequence)
        variance = sum((x - mean) ** 2 for x in entropy_sequence) / (len(entropy_sequence) - 1)

        return math.sqrt(variance)

    return calculer_std_entropy

def creer_fonction_logarithmic_prediction():
    """
    DIFF_LOG_PRED : Remplace Shannon par la formule de prédiction logarithmique
    """
    import math

    def calculer_log_prediction(donnee):
        """
        Calcule la formule de prédiction logarithmique P(S) = 0.45 + 0.35 * log(DIFF + 0.01)
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # DIFF approximé
        diff_value = abs(ratio_l4 - ratio_l5)

        # Formule logarithmique
        base_prob = 0.45
        scale_factor = 0.35
        offset = 0.01

        log_term = math.log(diff_value + offset)
        prob = base_prob + scale_factor * log_term

        return max(0.0, min(1.0, prob))

    return calculer_log_prediction

def creer_fonction_asymptotic_equipartition():
    """
    DIFF_AEP : Remplace Shannon par le théorème d'équipartition asymptotique
    """
    import math

    def calculer_aep_check(donnee):
        """
        Vérifie le théorème d'équipartition asymptotique approximé
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Approximation de -1/n log₂(p(x₁...xₙ))
        avg_ratio = (ratio_l4 + ratio_l5) / 2.0

        if avg_ratio <= 0:
            return 0.0

        # Log probabilité par symbole approximée
        log_prob_per_symbol = -math.log2(avg_ratio)

        return log_prob_per_symbol

    return calculer_aep_check

def creer_fonction_jensen_inequality():
    """
    DIFF_JENSEN : Remplace Shannon par la vérification de l'inégalité de Jensen
    """
    import math

    def calculer_jensen_check(donnee):
        """
        Vérifie l'inégalité de Jensen f(E[X]) ≤ E[f(X)] pour f convexe
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Valeurs et poids
        values = [ratio_l4, ratio_l5]
        weights = [0.5, 0.5]  # Équiprobables

        # Fonction convexe : f(x) = -x log₂(x) (entropie)
        def entropy_func(x):
            if x <= 0:
                return 0.0
            return -x * math.log2(x)

        # E[X]
        expected_x = sum(v * w for v, w in zip(values, weights))

        # f(E[X])
        f_expected = entropy_func(expected_x)

        # E[f(X)]
        expected_f = sum(entropy_func(v) * w for v, w in zip(values, weights))

        # Mesure de convexité (différence)
        return abs(expected_f - f_expected)

    return calculer_jensen_check

def creer_fonction_log_sum_inequality():
    """
    DIFF_LOG_SUM : Remplace Shannon par l'inégalité log-sum
    """
    import math

    def calculer_log_sum_inequality(donnee):
        """
        Calcule l'inégalité log-sum: ∑ a_i log(a_i/b_i) ≥ (∑ a_i) log((∑ a_i)/(∑ b_i))
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Valeurs a et b
        a_values = [ratio_l4, ratio_l5]
        b_values = [0.5, 0.5]  # Référence uniforme

        # Protection contre les valeurs nulles
        a_values = [max(0.001, a) for a in a_values]
        b_values = [max(0.001, b) for b in b_values]

        # Côté gauche : ∑ a_i log(a_i/b_i)
        left_side = sum(a * math.log2(a / b) for a, b in zip(a_values, b_values))

        # Côté droit : (∑ a_i) log((∑ a_i)/(∑ b_i))
        sum_a = sum(a_values)
        sum_b = sum(b_values)

        if sum_b > 0:
            right_side = sum_a * math.log2(sum_a / sum_b)
        else:
            right_side = 0.0

        # Retourner la différence (doit être ≥ 0)
        return max(0.0, left_side - right_side)

    return calculer_log_sum_inequality

def creer_fonction_entropy_concavity():
    """
    DIFF_CONCAVITY : Remplace Shannon par la vérification de concavité de l'entropie
    """
    import math

    def calculer_entropy_concavity(donnee):
        """
        Vérifie la concavité de l'entropie: H(λp₁ + (1-λ)p₂) ≥ λH(p₁) + (1-λ)H(p₂)
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Distributions p1 et p2
        p1 = [ratio_l4, 1 - ratio_l4]
        p2 = [ratio_l5, 1 - ratio_l5]
        lambda_val = 0.5  # Mélange équilibré

        # Distribution mélangée
        p_mix = [lambda_val * p1[i] + (1 - lambda_val) * p2[i] for i in range(2)]

        # Fonction d'entropie
        def entropy(p):
            return sum(-pi * math.log2(pi) if pi > 0 else 0 for pi in p)

        # H(mélange)
        h_mix = entropy(p_mix)

        # λH(p₁) + (1-λ)H(p₂)
        h_linear = lambda_val * entropy(p1) + (1 - lambda_val) * entropy(p2)

        # Mesure de concavité (doit être ≥ 0)
        return max(0.0, h_mix - h_linear)

    return calculer_entropy_concavity

def creer_fonction_shannon_mcmillan_breiman():
    """
    DIFF_SMB : Remplace Shannon par le théorème de Shannon-McMillan-Breiman
    """
    import math

    def calculer_smb_limit(donnee):
        """
        Approxime la limite du théorème de Shannon-McMillan-Breiman
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Séquence approximative de log probabilités
        log_probs = [-math.log2(ratio_l4) if ratio_l4 > 0 else 0,
                     -math.log2(ratio_l5) if ratio_l5 > 0 else 0]

        # Approximation de la limite
        if log_probs:
            return sum(log_probs) / len(log_probs)
        else:
            return 0.0

    return calculer_smb_limit

def creer_fonction_ergodic_entropy():
    """
    DIFF_ERGODIC : Remplace Shannon par l'estimation d'entropie ergodique
    """
    import math

    def calculer_ergodic_entropy(donnee):
        """
        Estime l'entropie d'un processus ergodique approximé
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Approximation des comptages de transitions
        # Transition L4 → L5
        transition_strength = abs(ratio_l5 - ratio_l4)

        # Entropie ergodique approximée
        if transition_strength > 0:
            return -transition_strength * math.log2(transition_strength) - (1 - transition_strength) * math.log2(1 - transition_strength)
        else:
            return 0.0

    return calculer_ergodic_entropy

def creer_fonction_channel_coding_theorem():
    """
    DIFF_CHANNEL : Remplace Shannon par le théorème de codage de canal
    """
    import math

    def calculer_channel_coding(donnee):
        """
        Vérifie si un taux de transmission est réalisable
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Capacité approximative basée sur l'information mutuelle
        correlation = 1.0 - abs(ratio_l4 - ratio_l5)
        capacity = correlation  # Approximation simple

        # Taux de transmission souhaité (basé sur la moyenne des ratios)
        rate = (ratio_l4 + ratio_l5) / 2.0

        # Marge de capacité
        return max(0.0, capacity - rate)

    return calculer_channel_coding

def creer_fonction_error_probability_bound():
    """
    DIFF_ERROR : Remplace Shannon par la borne de probabilité d'erreur
    """
    import math

    def calculer_error_bound(donnee):
        """
        Borne supérieure sur la probabilité d'erreur
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Capacité et taux
        capacity = 1.0 - abs(ratio_l4 - ratio_l5)  # Approximation
        rate = (ratio_l4 + ratio_l5) / 2.0
        block_length = 10  # Longueur de bloc fixe

        # Borne d'erreur approximative
        if capacity > rate:
            exponent = block_length * (capacity - rate)
            return min(1.0, 2**(-exponent))
        else:
            return 1.0  # Erreur certaine si taux > capacité

    return calculer_error_bound

def creer_fonction_sphere_packing_bound():
    """
    DIFF_SPHERE : Remplace Shannon par la borne de sphère
    """
    import math

    def calculer_sphere_packing(donnee):
        """
        Borne de sphère pour le nombre de mots de code
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Paramètres approximatifs
        block_length = 10
        error_correction_capability = int(abs(ratio_l4 - ratio_l5) * 5)  # 0-5 erreurs

        # Volume de la sphère de Hamming
        sphere_volume = sum(math.comb(block_length, i) for i in range(error_correction_capability + 1))

        # Nombre maximal de mots de code
        if sphere_volume > 0:
            max_codewords = (2 ** block_length) / sphere_volume
            return math.log2(max_codewords) if max_codewords > 0 else 0.0
        else:
            return 0.0

    return calculer_sphere_packing

def creer_fonction_comprehensive_entropy():
    """
    DIFF_COMPREHENSIVE : Remplace Shannon par l'analyse complète d'entropie
    """
    import math

    def calculer_comprehensive_entropy(donnee):
        """
        Analyse complète d'entropie sur une séquence approximée
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Séquence de données approximative
        data_sequence = [ratio_l4, ratio_l5]

        # Distribution empirique
        from collections import Counter
        counts = Counter(data_sequence)
        total = len(data_sequence)
        probabilities = [count/total for count in counts.values()]

        # Entropie de Shannon de base
        entropy = 0.0
        for p in probabilities:
            if p > 0:
                entropy -= p * math.log2(p)

        return entropy

    return calculer_comprehensive_entropy

def creer_fonction_relative_entropy():
    """
    DIFF_RELATIVE : Remplace Shannon par l'entropie relative (divergence KL)
    """
    import math

    def calculer_relative_entropy(donnee):
        """
        Calcule l'entropie relative D(p||q) = ∑ p(x) log₂(p(x)/q(x))
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Distributions p et q basées sur L4 et L5
        p = [ratio_l4, 1 - ratio_l4]
        q = [ratio_l5, 1 - ratio_l5]

        # Protection contre les valeurs nulles
        epsilon = 1e-12
        p = [max(epsilon, pi) for pi in p]
        q = [max(epsilon, qi) for qi in q]

        # Calcul de la divergence KL
        divergence = 0.0
        for pi, qi in zip(p, q):
            if pi > 0 and qi > 0:
                divergence += pi * math.log2(pi / qi)

        return divergence

    return calculer_relative_entropy

def creer_fonction_conditional_mutual_info():
    """
    DIFF_COND_MI : Remplace Shannon par l'information mutuelle conditionnelle
    """
    import math

    def calculer_conditional_mi(donnee):
        """
        Calcule l'information mutuelle conditionnelle I(X;Y|Z) approximée
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Approximation de I(X;Y|Z) = H(X|Z) - H(X|Y,Z)
        # Utilisation des ratios comme proxy pour les entropies conditionnelles

        # H(X|Z) approximé par ratio_l4
        h_x_given_z = -ratio_l4 * math.log2(ratio_l4) if ratio_l4 > 0 else 0

        # H(X|Y,Z) approximé par ratio_l5
        h_x_given_yz = -ratio_l5 * math.log2(ratio_l5) if ratio_l5 > 0 else 0

        # Information mutuelle conditionnelle
        cond_mi = h_x_given_z - h_x_given_yz

        return max(0.0, cond_mi)  # L'information mutuelle est toujours ≥ 0

    return calculer_conditional_mi

def creer_fonction_typical_set():
    """
    DIFF_TYPICAL : Remplace Shannon par l'analyse d'ensemble typique
    """
    import math

    def calculer_typical_set_bounds(donnee):
        """
        Calcule les bornes de l'ensemble typique T(n,ε)
        """
        ratio_l4 = donnee.get('ratio_l4', 0.5)
        ratio_l5 = donnee.get('ratio_l5', 0.5)

        # Entropie approximée
        entropy = (ratio_l4 + ratio_l5) / 2.0

        # Paramètres de l'ensemble typique
        n = 10  # Longueur de séquence fixe
        epsilon = 0.1  # Tolérance

        # Bornes de probabilité pour l'ensemble typique
        # P(x^n) ∈ [2^(-n(H+ε)), 2^(-n(H-ε))]
        if entropy > 0:
            prob_min = 2**(-n * (entropy + epsilon))
            prob_max = 2**(-n * (entropy - epsilon))

            # Taille de l'ensemble typique approximée
            size_bound = 2**(n * entropy)

            return math.log2(size_bound) if size_bound > 0 else 0.0
        else:
            return 0.0

    return calculer_typical_set_bounds

# ============================================================================
# DICTIONNAIRE DES 25 NOUVELLES MÉTRIQUES DIFF_X
# ============================================================================

def obtenir_toutes_metriques_diff():
    """
    Retourne le dictionnaire complet des 25 nouvelles métriques DIFF_X
    """
    return {
        # ENTROPIES FONDAMENTALES
        'DIFF_BERNOULLI': {
            'fonction': creer_fonction_bernoulli_entropy(),
            'description': 'DIFF basé sur entropie de Bernoulli h(a) = -a log₂(a) - (1-a) log₂(1-a)',
            'tranches': [(0.0, 0.1, "SIGNAL_PARFAIT"), (0.1, 0.3, "SIGNAL_EXCELLENT"),
                        (0.3, 0.5, "SIGNAL_BON"), (0.5, 0.7, "SIGNAL_ACCEPTABLE"),
                        (0.7, 0.9, "SIGNAL_RISQUÉ"), (0.9, 1.0, "SIGNAL_DOUTEUX")]
        },
        'DIFF_UNIFORM': {
            'fonction': creer_fonction_uniform_entropy(),
            'description': 'DIFF basé sur entropie uniforme H(uniform) = log₂(n)',
            'tranches': [(0.0, 1.0, "FAIBLE_DIVERSITÉ"), (1.0, 2.0, "DIVERSITÉ_MODÉRÉE"),
                        (2.0, 3.0, "FORTE_DIVERSITÉ"), (3.0, 4.0, "TRÈS_FORTE_DIVERSITÉ"),
                        (4.0, 10.0, "DIVERSITÉ_MAXIMALE")]
        },

        # ENTROPIES RELATIVES ET DIVERGENCES
        'DIFF_JOINT': {
            'fonction': creer_fonction_joint_entropy(),
            'description': 'DIFF basé sur entropie jointe H(X,Y)',
            'tranches': [(0.0, 0.5, "FAIBLE_COUPLAGE"), (0.5, 1.0, "COUPLAGE_MODÉRÉ"),
                        (1.0, 1.5, "FORT_COUPLAGE"), (1.5, 2.0, "TRÈS_FORT_COUPLAGE")]
        },

        # CHAÎNES DE MARKOV
        'DIFF_MARKOV': {
            'fonction': creer_fonction_markov_entropy(),
            'description': 'DIFF basé sur entropie de Markov H(Ξ) = -∑ μ(x) P_{xy} log₂(P_{xy})',
            'tranches': [(0.0, 0.2, "MARKOV_STABLE"), (0.2, 0.5, "MARKOV_MODÉRÉ"),
                        (0.5, 0.8, "MARKOV_INSTABLE"), (0.8, 1.0, "MARKOV_CHAOTIQUE")]
        },

        # SYSTÈMES DYNAMIQUES
        'DIFF_METRIC': {
            'fonction': creer_fonction_metric_entropy(),
            'description': 'DIFF basé sur entropie métrique h_μ(T)',
            'tranches': [(0.0, 0.1, "SYSTÈME_ORDONNÉ"), (0.1, 0.3, "SYSTÈME_STABLE"),
                        (0.3, 0.6, "SYSTÈME_MIXTE"), (0.6, 1.0, "SYSTÈME_CHAOTIQUE")]
        },
        'DIFF_BERNOULLI_SHIFT': {
            'fonction': creer_fonction_bernoulli_shift_entropy(),
            'description': 'DIFF basé sur entropie du décalage de Bernoulli B(p)',
            'tranches': [(0.0, 0.1, "SHIFT_ORDONNÉ"), (0.1, 0.3, "SHIFT_STABLE"),
                        (0.3, 0.6, "SHIFT_MIXTE"), (0.6, 1.0, "SHIFT_CHAOTIQUE")]
        },

        # THÉORIE DES CANAUX
        'DIFF_BSC': {
            'fonction': creer_fonction_binary_symmetric_channel(),
            'description': 'DIFF basé sur capacité canal binaire symétrique κ = 1 - h(α)',
            'tranches': [(0.0, 0.2, "CANAL_EXCELLENT"), (0.2, 0.5, "CANAL_BON"),
                        (0.5, 0.8, "CANAL_MOYEN"), (0.8, 1.0, "CANAL_DÉGRADÉ")]
        },
        'DIFF_ERASURE': {
            'fonction': creer_fonction_erasure_channel(),
            'description': 'DIFF basé sur capacité canal effaceur κ = 1 - α',
            'tranches': [(0.0, 0.2, "EFFACEMENT_FAIBLE"), (0.2, 0.5, "EFFACEMENT_MODÉRÉ"),
                        (0.5, 0.8, "EFFACEMENT_FORT"), (0.8, 1.0, "EFFACEMENT_CRITIQUE")]
        },

        # CODAGE ET COMPRESSION
        'DIFF_HUFFMAN': {
            'fonction': creer_fonction_huffman_efficiency(),
            'description': 'DIFF basé sur efficacité de Huffman',
            'tranches': [(0.0, 0.2, "COMPRESSION_FAIBLE"), (0.2, 0.5, "COMPRESSION_MODÉRÉE"),
                        (0.5, 0.8, "COMPRESSION_BONNE"), (0.8, 1.0, "COMPRESSION_OPTIMALE")]
        },

        # MÉTRIQUES SPÉCIALISÉES
        'DIFF_INVERSE': {
            'fonction': creer_fonction_inverse_entropy(),
            'description': 'DIFF basé sur métriques d\'entropie inverse (ordre vs chaos)',
            'tranches': [(0.0, 10.0, "CHAOS_TOTAL"), (10.0, 100.0, "DÉSORDRE_FORT"),
                        (100.0, 500.0, "ORDRE_MODÉRÉ"), (500.0, 1000.0, "ORDRE_FORT")]
        },
        'DIFF_STD': {
            'fonction': creer_fonction_standard_deviation_entropy(),
            'description': 'DIFF basé sur écart-type d\'entropie',
            'tranches': [(0.0, 0.05, "STABILITÉ_PARFAITE"), (0.05, 0.1, "STABILITÉ_EXCELLENTE"),
                        (0.1, 0.2, "STABILITÉ_BONNE"), (0.2, 0.5, "INSTABILITÉ_MODÉRÉE")]
        },
        'DIFF_LOG_PRED': {
            'fonction': creer_fonction_logarithmic_prediction(),
            'description': 'DIFF basé sur formule prédiction logarithmique P(S) = 0.45 + 0.35*log(DIFF+0.01)',
            'tranches': [(0.0, 0.3, "PRÉDICTION_FAIBLE"), (0.3, 0.5, "PRÉDICTION_MODÉRÉE"),
                        (0.5, 0.7, "PRÉDICTION_FORTE"), (0.7, 1.0, "PRÉDICTION_EXCELLENTE")]
        },

        # THÉORÈMES ASYMPTOTIQUES
        'DIFF_AEP': {
            'fonction': creer_fonction_asymptotic_equipartition(),
            'description': 'DIFF basé sur théorème d\'équipartition asymptotique',
            'tranches': [(0.0, 1.0, "AEP_FAIBLE"), (1.0, 3.0, "AEP_MODÉRÉ"),
                        (3.0, 5.0, "AEP_FORT"), (5.0, 10.0, "AEP_TRÈS_FORT")]
        },
        'DIFF_JENSEN': {
            'fonction': creer_fonction_jensen_inequality(),
            'description': 'DIFF basé sur inégalité de Jensen f(E[X]) ≤ E[f(X)]',
            'tranches': [(0.0, 0.01, "JENSEN_RESPECTÉE"), (0.01, 0.05, "JENSEN_LÉGÈRE_VIOLATION"),
                        (0.05, 0.1, "JENSEN_VIOLATION_MODÉRÉE"), (0.1, 1.0, "JENSEN_FORTE_VIOLATION")]
        },
        'DIFF_LOG_SUM': {
            'fonction': creer_fonction_log_sum_inequality(),
            'description': 'DIFF basé sur inégalité log-sum',
            'tranches': [(0.0, 0.1, "LOG_SUM_FAIBLE"), (0.1, 0.5, "LOG_SUM_MODÉRÉ"),
                        (0.5, 1.0, "LOG_SUM_FORT"), (1.0, 2.0, "LOG_SUM_TRÈS_FORT")]
        },
        'DIFF_CONCAVITY': {
            'fonction': creer_fonction_entropy_concavity(),
            'description': 'DIFF basé sur concavité entropie H(λp₁+(1-λ)p₂) ≥ λH(p₁)+(1-λ)H(p₂)',
            'tranches': [(0.0, 0.01, "CONCAVITÉ_PARFAITE"), (0.01, 0.05, "CONCAVITÉ_BONNE"),
                        (0.05, 0.1, "CONCAVITÉ_ACCEPTABLE"), (0.1, 0.5, "CONCAVITÉ_DÉGRADÉE")]
        },
        'DIFF_SMB': {
            'fonction': creer_fonction_shannon_mcmillan_breiman(),
            'description': 'DIFF basé sur théorème Shannon-McMillan-Breiman',
            'tranches': [(0.0, 1.0, "SMB_CONVERGENCE_RAPIDE"), (1.0, 3.0, "SMB_CONVERGENCE_MODÉRÉE"),
                        (3.0, 5.0, "SMB_CONVERGENCE_LENTE"), (5.0, 10.0, "SMB_DIVERGENCE")]
        },
        'DIFF_ERGODIC': {
            'fonction': creer_fonction_ergodic_entropy(),
            'description': 'DIFF basé sur estimation entropie ergodique',
            'tranches': [(0.0, 0.1, "ERGODICITÉ_FORTE"), (0.1, 0.3, "ERGODICITÉ_MODÉRÉE"),
                        (0.3, 0.6, "ERGODICITÉ_FAIBLE"), (0.6, 1.0, "NON_ERGODIQUE")]
        },

        # THÉORIE DES CANAUX AVANCÉE
        'DIFF_CHANNEL': {
            'fonction': creer_fonction_channel_coding_theorem(),
            'description': 'DIFF basé sur théorème codage de canal',
            'tranches': [(0.0, 0.2, "CANAL_SATURÉ"), (0.2, 0.5, "CANAL_CHARGÉ"),
                        (0.5, 0.8, "CANAL_DISPONIBLE"), (0.8, 1.0, "CANAL_LIBRE")]
        },
        'DIFF_ERROR': {
            'fonction': creer_fonction_error_probability_bound(),
            'description': 'DIFF basé sur borne probabilité d\'erreur',
            'tranches': [(0.0, 0.01, "ERREUR_NÉGLIGEABLE"), (0.01, 0.1, "ERREUR_FAIBLE"),
                        (0.1, 0.5, "ERREUR_MODÉRÉE"), (0.5, 1.0, "ERREUR_CRITIQUE")]
        },
        'DIFF_SPHERE': {
            'fonction': creer_fonction_sphere_packing_bound(),
            'description': 'DIFF basé sur borne de sphère (sphere packing)',
            'tranches': [(0.0, 2.0, "PACKING_DENSE"), (2.0, 5.0, "PACKING_MODÉRÉ"),
                        (5.0, 8.0, "PACKING_LÂCHE"), (8.0, 10.0, "PACKING_TRÈS_LÂCHE")]
        },

        # ANALYSE COMPLÈTE
        'DIFF_COMPREHENSIVE': {
            'fonction': creer_fonction_comprehensive_entropy(),
            'description': 'DIFF basé sur analyse complète d\'entropie multi-échelle',
            'tranches': [(0.0, 0.2, "ANALYSE_SIMPLE"), (0.2, 0.5, "ANALYSE_MODÉRÉE"),
                        (0.5, 0.8, "ANALYSE_COMPLEXE"), (0.8, 1.0, "ANALYSE_TRÈS_COMPLEXE")]
        },

        # MÉTRIQUES RELATIVES ET CONDITIONNELLES
        'DIFF_RELATIVE': {
            'fonction': creer_fonction_relative_entropy(),
            'description': 'DIFF basé sur entropie relative (divergence KL) D(p||q) = ∑ p(x) log₂(p(x)/q(x))',
            'tranches': [(0.0, 0.1, "DIVERGENCE_FAIBLE"), (0.1, 0.5, "DIVERGENCE_MODÉRÉE"),
                        (0.5, 1.0, "DIVERGENCE_FORTE"), (1.0, 2.0, "DIVERGENCE_TRÈS_FORTE")]
        },
        'DIFF_COND_MI': {
            'fonction': creer_fonction_conditional_mutual_info(),
            'description': 'DIFF basé sur information mutuelle conditionnelle I(X;Y|Z) = H(X|Z) - H(X|Y,Z)',
            'tranches': [(0.0, 0.1, "INFO_FAIBLE"), (0.1, 0.3, "INFO_MODÉRÉE"),
                        (0.3, 0.6, "INFO_FORTE"), (0.6, 1.0, "INFO_TRÈS_FORTE")]
        },
        'DIFF_TYPICAL': {
            'fonction': creer_fonction_typical_set(),
            'description': 'DIFF basé sur ensemble typique T(n,ε) et bornes asymptotiques',
            'tranches': [(0.0, 2.0, "ENSEMBLE_PETIT"), (2.0, 5.0, "ENSEMBLE_MODÉRÉ"),
                        (5.0, 8.0, "ENSEMBLE_GRAND"), (8.0, 10.0, "ENSEMBLE_TRÈS_GRAND")]
        }
    }

# ============================================================================
# INTÉGRATION DANS ANALYSEUR MÉTRIQUE GÉNÉRIQUE
# ============================================================================

def creer_tous_analyseurs_diff():
    """
    Crée tous les analyseurs DIFF_X basés sur les 25 nouvelles métriques

    Returns:
        dict: Dictionnaire des analyseurs {nom_metrique: AnalyseurMetriqueGenerique}
    """
    toutes_metriques = obtenir_toutes_metriques_diff()
    analyseurs = {}

    for nom_metrique, config in toutes_metriques.items():
        analyseur = AnalyseurMetriqueGenerique(
            nom_metrique=nom_metrique,
            fonction_calcul=config['fonction'],
            tranches_qualite=config['tranches']
        )
        analyseurs[nom_metrique] = analyseur

    return analyseurs

def analyser_toutes_metriques_diff(donnees):
    """
    Analyse toutes les métriques DIFF_X sur les données fournies

    Args:
        donnees: Liste des données avec ratio_l4, ratio_l5, pattern, etc.

    Returns:
        dict: Résultats d'analyse pour toutes les métriques
    """
    print("\n🔥 ANALYSE COMPLÈTE DES 25 MÉTRIQUES DIFF_X")
    print("=" * 60)

    analyseurs = creer_tous_analyseurs_diff()
    resultats = {}

    for nom_metrique, analyseur in analyseurs.items():
        print(f"\n📊 Analyse {nom_metrique}...")

        try:
            # Calculer la métrique sur toutes les données
            valeurs_metrique = analyseur.calculer_metrique_sur_donnees(donnees)

            # Analyser les tranches de qualité
            resultats_tranches = analyseur.analyser_tranches_qualite(donnees, valeurs_metrique)

            # Calculer les corrélations
            correlations = analyseur.analyser_correlations_patterns(donnees, valeurs_metrique)

            # Stocker les résultats
            resultats[nom_metrique] = {
                'valeurs': valeurs_metrique,
                'tranches': resultats_tranches,
                'correlations': correlations,
                'description': obtenir_toutes_metriques_diff()[nom_metrique]['description']
            }

            print(f"   ✅ {len(valeurs_metrique)} valeurs calculées")

        except Exception as e:
            print(f"   ❌ Erreur: {e}")
            resultats[nom_metrique] = {'erreur': str(e)}

    return resultats

def generer_rapport_complet_diff_x(donnees, fichier_sortie="rapport_25_metriques_diff.txt"):
    """
    Génère un rapport complet pour toutes les 25 métriques DIFF_X

    Args:
        donnees: Données d'analyse
        fichier_sortie: Nom du fichier de rapport
    """
    print(f"\n📝 GÉNÉRATION RAPPORT COMPLET : {fichier_sortie}")
    print("=" * 60)

    resultats = analyser_toutes_metriques_diff(donnees)

    with open(fichier_sortie, 'w', encoding='utf-8') as f:
        f.write("RAPPORT COMPLET DES 25 MÉTRIQUES DIFF_X\n")
        f.write("=" * 80 + "\n\n")
        f.write(f"Analyse effectuée sur {len(donnees)} mains\n")
        f.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        f.write("ARCHITECTURE DIFF GÉNÉRALISÉE\n")
        f.write("-" * 40 + "\n")
        f.write("Pour chaque métrique F et chaque main n ≥ 5 :\n")
        f.write("1. seq_L4 = [index5_{n-3}, index5_{n-2}, index5_{n-1}, index5_n]\n")
        f.write("2. seq_L5 = [index5_{n-4}, index5_{n-3}, index5_{n-2}, index5_{n-1}, index5_n]\n")
        f.write("3. seq_globale = [index5_1, index5_2, ..., index5_n]\n")
        f.write("4. signature_L4 = F(seq_L4)\n")
        f.write("5. signature_L5 = F(seq_L5)\n")
        f.write("6. signature_globale = F(seq_globale)\n")
        f.write("7. ratio_L4 = signature_L4 / signature_globale\n")
        f.write("8. ratio_L5 = signature_L5 / signature_globale\n")
        f.write("9. DIFF_F = |ratio_L4 - ratio_L5|\n\n")

        # Résumé des métriques
        f.write("RÉSUMÉ DES 25 MÉTRIQUES DIFF_X\n")
        f.write("-" * 40 + "\n")

        metriques_reussies = 0
        for nom_metrique, resultat in resultats.items():
            if 'erreur' not in resultat:
                metriques_reussies += 1
                valeurs = resultat['valeurs']
                f.write(f"\n{nom_metrique}:\n")
                f.write(f"  Description: {resultat['description']}\n")
                f.write(f"  Valeurs calculées: {len(valeurs)}\n")
                if valeurs:
                    f.write(f"  Min: {min(valeurs):.6f}\n")
                    f.write(f"  Max: {max(valeurs):.6f}\n")
                    f.write(f"  Moyenne: {sum(valeurs)/len(valeurs):.6f}\n")
            else:
                f.write(f"\n{nom_metrique}: ERREUR - {resultat['erreur']}\n")

        f.write(f"\nMÉTRIQUES RÉUSSIES: {metriques_reussies}/25\n")

        # Analyse détaillée pour chaque métrique réussie
        f.write("\n\nANALYSE DÉTAILLÉE PAR MÉTRIQUE\n")
        f.write("=" * 80 + "\n")

        for nom_metrique, resultat in resultats.items():
            if 'erreur' not in resultat:
                f.write(f"\n{nom_metrique}\n")
                f.write("-" * len(nom_metrique) + "\n")
                f.write(f"Description: {resultat['description']}\n\n")

                # Statistiques des tranches
                if 'tranches' in resultat:
                    f.write("ANALYSE PAR TRANCHES:\n")
                    for tranche_info in resultat['tranches']:
                        f.write(f"  {tranche_info}\n")

                # Corrélations
                if 'correlations' in resultat:
                    f.write(f"\nCORRÉLATIONS:\n")
                    for corr_info in resultat['correlations']:
                        f.write(f"  {corr_info}\n")

                f.write("\n" + "="*60 + "\n")

    print(f"✅ Rapport généré: {fichier_sortie}")
    print(f"📊 Métriques analysées: {metriques_reussies}/25")

    return fichier_sortie

# ============================================================================
# FONCTION DE TEST DES 25 NOUVELLES MÉTRIQUES
# ============================================================================

def tester_25_metriques_diff():
    """
    Teste toutes les 25 nouvelles métriques DIFF_X avec des données simulées
    """
    print("\n🧪 TEST DES 25 NOUVELLES MÉTRIQUES DIFF_X")
    print("=" * 60)

    # Créer des données de test simulées
    donnees_test = []
    for i in range(100):
        donnee = {
            'ratio_l4': 0.3 + (i % 50) * 0.01,  # Varie de 0.3 à 0.8
            'ratio_l5': 0.4 + (i % 40) * 0.01,  # Varie de 0.4 à 0.8
            'pattern': i % 2,  # Alternance S/O
            'main_number': i + 5,
            'index3': ['BANKER', 'PLAYER', 'TIE'][i % 3]
        }
        donnees_test.append(donnee)

    print(f"📊 Données de test créées: {len(donnees_test)} échantillons")

    # Tester chaque métrique individuellement
    toutes_metriques = obtenir_toutes_metriques_diff()
    resultats_test = {}

    for nom_metrique, config in toutes_metriques.items():
        print(f"\n🔍 Test {nom_metrique}...")

        try:
            # Tester la fonction de calcul
            fonction = config['fonction']

            # Test sur quelques échantillons
            valeurs_test = []
            for i in range(min(10, len(donnees_test))):
                valeur = fonction(donnees_test[i])
                valeurs_test.append(valeur)

            # Vérifications
            if len(valeurs_test) > 0:
                min_val = min(valeurs_test)
                max_val = max(valeurs_test)
                moy_val = sum(valeurs_test) / len(valeurs_test)

                print(f"   ✅ Calculs réussis: {len(valeurs_test)} valeurs")
                print(f"   📈 Min: {min_val:.6f}, Max: {max_val:.6f}, Moy: {moy_val:.6f}")

                resultats_test[nom_metrique] = {
                    'statut': 'SUCCÈS',
                    'valeurs': valeurs_test,
                    'min': min_val,
                    'max': max_val,
                    'moyenne': moy_val,
                    'description': config['description']
                }
            else:
                print(f"   ❌ Aucune valeur calculée")
                resultats_test[nom_metrique] = {'statut': 'ÉCHEC', 'erreur': 'Aucune valeur'}

        except Exception as e:
            print(f"   ❌ Erreur: {e}")
            resultats_test[nom_metrique] = {'statut': 'ERREUR', 'erreur': str(e)}

    # Résumé des tests
    print(f"\n📋 RÉSUMÉ DES TESTS")
    print("=" * 40)

    succes = sum(1 for r in resultats_test.values() if r.get('statut') == 'SUCCÈS')
    echecs = sum(1 for r in resultats_test.values() if r.get('statut') in ['ÉCHEC', 'ERREUR'])

    print(f"✅ Métriques réussies: {succes}/25")
    print(f"❌ Métriques échouées: {echecs}/25")
    print(f"📊 Taux de réussite: {(succes/25)*100:.1f}%")

    # Afficher les erreurs s'il y en a
    if echecs > 0:
        print(f"\n❌ MÉTRIQUES EN ERREUR:")
        for nom, resultat in resultats_test.items():
            if resultat.get('statut') in ['ÉCHEC', 'ERREUR']:
                print(f"   {nom}: {resultat.get('erreur', 'Erreur inconnue')}")

    return resultats_test

def demo_analyse_complete_25_metriques():
    """
    Démonstration complète des 25 métriques DIFF_X
    """
    print("\n🎯 DÉMONSTRATION COMPLÈTE DES 25 MÉTRIQUES DIFF_X")
    print("=" * 70)

    # 1. Test des métriques
    print("\n1️⃣ PHASE 1: Test des fonctions de calcul")
    resultats_test = tester_25_metriques_diff()

    # 2. Analyse avec données simulées plus importantes
    print("\n2️⃣ PHASE 2: Analyse sur données étendues")

    # Créer un jeu de données plus important
    donnees_etendues = []
    for i in range(500):
        donnee = {
            'ratio_l4': 0.1 + (i % 80) * 0.01,  # Varie de 0.1 à 0.9
            'ratio_l5': 0.2 + (i % 70) * 0.01,  # Varie de 0.2 à 0.9
            'pattern': i % 2,
            'main_number': i + 5,
            'index3': ['BANKER', 'PLAYER', 'TIE'][i % 3],
            'diff': abs((0.1 + (i % 80) * 0.01) - (0.2 + (i % 70) * 0.01))
        }
        donnees_etendues.append(donnee)

    print(f"📊 Données étendues: {len(donnees_etendues)} échantillons")

    # 3. Générer le rapport complet
    print("\n3️⃣ PHASE 3: Génération du rapport complet")
    try:
        fichier_rapport = generer_rapport_complet_diff_x(donnees_etendues)
        print(f"✅ Rapport généré: {fichier_rapport}")
    except Exception as e:
        print(f"❌ Erreur génération rapport: {e}")

    # 4. Résumé final
    print("\n4️⃣ RÉSUMÉ FINAL")
    print("=" * 30)

    succes = sum(1 for r in resultats_test.values() if r.get('statut') == 'SUCCÈS')
    print(f"🎯 Métriques DIFF_X implémentées: 25")
    print(f"✅ Métriques fonctionnelles: {succes}")
    print(f"📈 Architecture DIFF reproduite pour {succes} formules d'entropie")
    print(f"🔬 Chaque métrique suit exactement la même logique:")
    print(f"   • Calcul signatures L4, L5, globale avec formule F")
    print(f"   • Ratios: ratio_L4 = F(L4)/F(globale), ratio_L5 = F(L5)/F(globale)")
    print(f"   • DIFF_F = |ratio_L4 - ratio_L5|")

    return resultats_test

def extraire_donnees_avec_diff(evolutions_entropiques):
    """
    Extrait les données d'analyse avec calcul DIFF depuis les évolutions entropiques

    Args:
        evolutions_entropiques: Données d'évolution entropique de l'analyseur

    Returns:
        list: Liste des données d'analyse avec DIFF
    """
    print("🔄 EXTRACTION DES DONNÉES AVEC DIFF")
    print("-" * 50)

    # Débogage : examiner la structure des données
    print(f"🔍 Type des données reçues: {type(evolutions_entropiques)}")
    print(f"🔍 Nombre d'éléments: {len(evolutions_entropiques) if hasattr(evolutions_entropiques, '__len__') else 'N/A'}")

    if hasattr(evolutions_entropiques, 'items'):
        print(f"🔍 Premières clés: {list(evolutions_entropiques.keys())[:5]}")
        # Examiner la première évolution
        first_key = next(iter(evolutions_entropiques.keys())) if evolutions_entropiques else None
        if first_key:
            first_evolution = evolutions_entropiques[first_key]
            print(f"🔍 Structure première évolution: {type(first_evolution)}")
            if isinstance(first_evolution, dict):
                print(f"🔍 Clés disponibles: {list(first_evolution.keys())}")
                # Examiner plus en détail la structure
                for key, value in first_evolution.items():
                    print(f"🔍   {key}: {type(value)} - {len(value) if hasattr(value, '__len__') and not isinstance(value, str) else value}")
                    if key == 'mains_analysees' and isinstance(value, list) and len(value) > 0:
                        print(f"🔍     Première main analysée: {type(value[0])} - {list(value[0].keys()) if isinstance(value[0], dict) else value[0]}")

    donnees_analyse = []
    parties_traitees = 0

    for partie_id, evolution in evolutions_entropiques.items():
        if evolution and 'patterns' in evolution:
            patterns = evolution['patterns']
            ratios_l4 = evolution.get('ratios_l4', [])
            ratios_l5 = evolution.get('ratios_l5', [])
            index3 = evolution.get('index3', [])
            diff_l4_vars = evolution.get('diff_l4', [])
            diff_l5_vars = evolution.get('diff_l5', [])

            # Utiliser données main i pour prédire pattern i→i+1
            for i in range(len(patterns)):
                if i < len(ratios_l4) and i < len(ratios_l5) and i < len(index3):
                    # OPTIMAL : Données main i pour prédire pattern i→i+1
                    ratio_l4_main = ratios_l4[i]      # Main i (état actuel)
                    ratio_l5_main = ratios_l5[i]      # Main i (état actuel)
                    pattern = patterns[i]             # Pattern i→i+1 (prochaine transition)
                    index3_main = index3[i]           # Index3 main i

                    # Calculer les différentiels si disponibles
                    diff_l4 = diff_l4_vars[i] if i < len(diff_l4_vars) else 0.0
                    diff_l5 = diff_l5_vars[i] if i < len(diff_l5_vars) else 0.0

                    # CALCUL CRITIQUE : DIFF = |L4-L5| (cohérence)
                    diff_coherence = abs(ratio_l4_main - ratio_l5_main)

                    # Ignorer les patterns E (TIE) pour cette analyse
                    if pattern in ['S', 'O']:
                        donnees_analyse.append({
                            'partie_id': partie_id,
                            'main': i + 5,  # Main réelle (FAIT OBJECTIF: analyse commence à main 5)
                            'ratio_l4': ratio_l4_main,
                            'ratio_l5': ratio_l5_main,
                            'diff_l4': diff_l4,
                            'diff_l5': diff_l5,
                            'diff': diff_coherence,  # VARIABLE DIFF AJOUTÉE
                            'pattern': pattern,
                            'index3': index3_main
                        })

        parties_traitees += 1
        if parties_traitees % 10000 == 0:
            print(f"   📊 {parties_traitees:,} parties traitées...")

    print(f"✅ {len(donnees_analyse):,} points de données extraits AVEC DIFF")
    return donnees_analyse

def analyser_conditions_predictives_so_avec_diff_x(nom_metrique, fonction_calcul, tranches_qualite, donnees_base):
    """
    🎯 ANALYSE CONDITIONS PRÉDICTIVES AVEC MÉTRIQUE DIFF_X (DONNÉES DÉJÀ CHARGÉES)

    Utilise les données déjà extraites et applique une métrique alternative.

    Args:
        nom_metrique (str): Nom de la métrique (ex: "DIFF_BERNOULLI")
        fonction_calcul (callable): Fonction de calcul de la métrique
        tranches_qualite (list): Tranches de qualité pour la métrique
        donnees_base (list): Données déjà extraites avec ratios L4/L5

    Returns:
        str: Nom du fichier de rapport généré
    """
    print(f"🔬 ANALYSE CONDITIONS PRÉDICTIVES S/O AVEC {nom_metrique}")
    print(f"📊 Utilisation des données déjà chargées")
    print(f"🎯 {nom_metrique} = Métrique d'entropie alternative")
    print("-" * 50)

    try:
        # PHASE 1 : RECALCUL DE LA MÉTRIQUE SUR LES DONNÉES EXISTANTES
        print(f"🔄 Recalcul {nom_metrique} sur {len(donnees_base):,} points...")

        donnees_analyse = []
        for donnee in donnees_base:
            # Recalculer la métrique avec la nouvelle fonction
            metrique_value = fonction_calcul(donnee['ratio_l4'], donnee['ratio_l5'])

            # Créer une nouvelle donnée avec la métrique alternative
            nouvelle_donnee = donnee.copy()
            nouvelle_donnee['diff'] = metrique_value  # Remplacer DIFF par la nouvelle métrique
            donnees_analyse.append(nouvelle_donnee)

        print(f"✅ {len(donnees_analyse):,} points recalculés avec {nom_metrique}")

        # PHASE 2: ANALYSE EXHAUSTIVE (IDENTIQUE AU DIFF ORIGINAL)
        print(f"📊 Analyse exhaustive avec {nom_metrique}...")
        conditions_s, conditions_o = analyser_toutes_conditions_avec_diff(donnees_analyse)

        # PHASE 3: GÉNÉRATION DU TABLEAU PRÉDICTIF
        print(f"📊 Génération tableau prédictif...")

        # Calculer les corrélations
        correlations_stats = calculer_correlations_essentielles(donnees_analyse)

        nom_rapport = generer_tableau_predictif_avec_diff(conditions_s, conditions_o, len(donnees_analyse), correlations_stats)

        # Renommer le fichier pour inclure le nom de la métrique
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        nouveau_nom = f"tableau_predictif_avec_{nom_metrique.lower()}_{timestamp}.txt"

        if os.path.exists(nom_rapport):
            os.rename(nom_rapport, nouveau_nom)
            nom_rapport = nouveau_nom

        print(f"✅ Tableau prédictif AVEC {nom_metrique} généré: {nom_rapport}")

        return nom_rapport

    except Exception as e:
        print(f"❌ Erreur durant l'analyse {nom_metrique}: {e}")
        import traceback
        traceback.print_exc()
        return None

def generer_tous_tableaux_predictifs_diff_x():
    """
    🎯 FONCTION PRINCIPALE : GÉNÉRATION DE TOUS LES TABLEAUX PRÉDICTIFS DIFF_X

    ARCHITECTURE OPTIMISÉE : CHARGEMENT JSON UNE SEULE FOIS
    Reproduit exactement l'architecture du rapport DIFF original pour les 25 variantes.
    """
    print("🚀 GÉNÉRATION DE TOUS LES TABLEAUX PRÉDICTIFS DIFF_X")
    print("=" * 80)
    print("📊 Architecture reproduite depuis DIFF original")
    print("🔗 Connexion aux données réelles INDEX5")
    print("📈 CHARGEMENT JSON UNE SEULE FOIS - OPTIMISÉ")

    try:
        # ====================================================================
        # PHASE 1 : CHARGEMENT UNIQUE DES DONNÉES (UNE SEULE FOIS)
        # ====================================================================
        print(f"\n📊 PHASE 1: CHARGEMENT UNIQUE DES DONNÉES")
        print("-" * 50)

        # Import des modules d'analyse
        from analyseur_transitions_index5 import AnalyseurEvolutionEntropique, AnalyseurEvolutionRatios

        dataset_path = "dataset_baccarat_lupasco_20250626_044753.json"
        if not os.path.exists(dataset_path):
            print(f"❌ Dataset non trouvé: {dataset_path}")
            return False

        print("🔄 Chargement analyseur entropique (UNE SEULE FOIS)...")
        analyseur_entropique = AnalyseurEvolutionEntropique(dataset_path)

        # Analyse entropique (sans limite prédéterminée)
        print("🔄 Analyse entropique en cours (UNE SEULE FOIS)...")
        resultats_entropiques = analyseur_entropique.analyser_toutes_parties_entropiques()
        print(f"✅ {resultats_entropiques['parties_reussies']:,} parties analysées")

        print("🔄 Chargement analyseur ratios (UNE SEULE FOIS)...")
        analyseur_ratios = AnalyseurEvolutionRatios(analyseur_entropique)

        print("🔄 Analyse ratios en cours (UNE SEULE FOIS)...")
        analyseur_ratios.analyser_evolution_toutes_parties()
        print(f"✅ Ratios calculés pour toutes les parties")

        # ====================================================================
        # PHASE 2 : EXTRACTION UNIQUE DES DONNÉES (UNE SEULE FOIS)
        # ====================================================================
        print(f"\n📊 PHASE 2: EXTRACTION UNIQUE DES DONNÉES")
        print("-" * 50)

        donnees_base = []
        parties_traitees = 0

        # UTILISER LA MÊME LOGIQUE QUE LE DIFF ORIGINAL (analyseur_ratios.evolutions_ratios)
        for partie_id, evolution_ratios in analyseur_ratios.evolutions_ratios.items():
            if 'erreur' in evolution_ratios:
                continue

            # Vérifier la présence de toutes les données nécessaires (IDENTIQUE AU DIFF ORIGINAL)
            if not all(key in evolution_ratios for key in ['ratios_l4', 'ratios_l5', 'patterns_soe', 'index3_resultats']):
                continue

            ratios_l4 = evolution_ratios['ratios_l4']
            ratios_l5 = evolution_ratios['ratios_l5']
            patterns = evolution_ratios['patterns_soe']
            index3 = evolution_ratios['index3_resultats']
            diff_l4_vars = evolution_ratios.get('diff_l4_variations', [])
            diff_l5_vars = evolution_ratios.get('diff_l5_variations', [])

            # Utiliser données main i pour prédire pattern i→i+1 (IDENTIQUE AU DIFF ORIGINAL)
            for i in range(len(patterns)):
                if i < len(ratios_l4) and i < len(ratios_l5) and i < len(index3):
                    ratio_l4_main = ratios_l4[i]
                    ratio_l5_main = ratios_l5[i]
                    pattern = patterns[i]
                    index3_main = index3[i]

                    diff_l4 = diff_l4_vars[i] if i < len(diff_l4_vars) else 0.0
                    diff_l5 = diff_l5_vars[i] if i < len(diff_l5_vars) else 0.0

                    # DIFF original = |L4-L5| (IDENTIQUE AU DIFF ORIGINAL)
                    diff_coherence = abs(ratio_l4_main - ratio_l5_main)

                    # Ignorer les patterns E (TIE) pour cette analyse
                    if pattern in ['S', 'O']:
                        donnees_base.append({
                            'partie_id': partie_id,
                            'main': i + 5,  # Main réelle (FAIT OBJECTIF: analyse commence à main 5)
                            'ratio_l4': ratio_l4_main,
                            'ratio_l5': ratio_l5_main,
                            'diff_l4': diff_l4,
                            'diff_l5': diff_l5,
                            'diff': diff_coherence,  # VARIABLE DIFF AJOUTÉE
                            'pattern': pattern,
                            'index3': index3_main
                        })

            parties_traitees += 1
            if parties_traitees % 1000 == 0:
                print(f"   📊 {parties_traitees:,} parties traitées...")

        print(f"✅ {len(donnees_base):,} points de données extraits (BASE COMMUNE)")

        if not donnees_base:
            print("❌ Aucune donnée extraite")
            return False

        # ====================================================================
        # PHASE 3 : GÉNÉRATION DES 25 TABLEAUX (RÉUTILISATION DES DONNÉES)
        # ====================================================================
        print(f"\n📊 PHASE 3: GÉNÉRATION DES 25 TABLEAUX (DONNÉES RÉUTILISÉES)")
        print("-" * 50)

        metriques_diff_x = obtenir_toutes_metriques_diff()
        rapports_generes = []

        for i, (nom_metrique, config_metrique) in enumerate(metriques_diff_x.items(), 1):
            print(f"\n🔍 [{i:2d}/25] Génération tableau pour {nom_metrique}...")

            try:
                nom_rapport = analyser_conditions_predictives_so_avec_diff_x(
                    nom_metrique=nom_metrique,
                    fonction_calcul=config_metrique['fonction'],
                    tranches_qualite=config_metrique['tranches'],
                    donnees_base=donnees_base  # RÉUTILISATION DES DONNÉES
                )

                if nom_rapport:
                    rapports_generes.append(nom_rapport)
                    print(f"   ✅ {nom_rapport}")
                else:
                    print(f"   ❌ Échec pour {nom_metrique}")

            except Exception as e:
                print(f"   ❌ Erreur pour {nom_metrique}: {e}")
                continue

        # ====================================================================
        # PHASE 4 : RÉSUMÉ FINAL
        # ====================================================================
        print(f"\n📊 PHASE 4: RÉSUMÉ FINAL")
        print("=" * 50)

        print(f"🎯 Tableaux prédictifs générés: {len(rapports_generes)}/25")
        print(f"📊 Données analysées: {len(donnees_base):,} points (RÉUTILISÉES)")
        print(f"🔗 Architecture DIFF reproduite pour {len(rapports_generes)} métriques")
        print(f"⚡ OPTIMISATION: JSON chargé UNE SEULE FOIS")

        if rapports_generes:
            print(f"\n📁 RAPPORTS GÉNÉRÉS:")
            for rapport in rapports_generes:
                print(f"   • {rapport}")

        return len(rapports_generes) == 25

    except Exception as e:
        print(f"❌ Erreur durant la génération: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Lancer la démonstration si le script est exécuté directement
    demo_analyse_complete_25_metriques()


# ============================================================================
# EXEMPLE D'UTILISATION DE LA CLASSE GÉNÉRIQUE
# ============================================================================

def exemple_analyse_shannon_entropy():
    """
    Exemple d'utilisation de l'analyseur générique avec l'entropie de Shannon

    Returns:
        bool: True si l'analyse réussit
    """
    print("🔬 EXEMPLE : ANALYSE AVEC ENTROPIE DE SHANNON")
    print("=" * 60)

    try:
        # Créer l'analyseur pour l'entropie de Shannon
        fonction_shannon = creer_fonction_shannon_entropy()

        # Tranches de qualité spécifiques à l'entropie de Shannon (0 à 1 bit)
        tranches_shannon = [
            (0.0, 0.1, "SIGNAL_PARFAIT"),
            (0.1, 0.2, "SIGNAL_EXCELLENT"),
            (0.2, 0.4, "SIGNAL_TRÈS_BON"),
            (0.4, 0.6, "SIGNAL_BON"),
            (0.6, 0.8, "SIGNAL_ACCEPTABLE"),
            (0.8, 0.9, "SIGNAL_RISQUÉ"),
            (0.9, 0.95, "SIGNAL_DOUTEUX"),
            (0.95, 0.99, "SIGNAL_TRÈS_DOUTEUX"),
            (0.99, 10.0, "SIGNAL_INUTILISABLE")
        ]

        # Formules dérivées pour Shannon
        formules_shannon = {
            "Entropie_Maximum": "H_max = log₂(n) pour n symboles équiprobables",
            "Efficacité": "η = H(X) / H_max",
            "Redondance": "R = 1 - η = 1 - H(X)/H_max",
            "Capacité_Information": "C = H_max - H(X)"
        }

        analyseur_shannon = AnalyseurMetriqueGenerique(
            nom_metrique="SHANNON_ENTROPY",
            fonction_calcul=fonction_shannon,
            tranches_qualite=tranches_shannon,
            formules_derivees=formules_shannon
        )

        print(f"✅ Analyseur Shannon créé avec {len(tranches_shannon)} tranches de qualité")
        print(f"📊 Formules dérivées: {len(formules_shannon)} disponibles")

        # Simuler des données d'analyse (normalement extraites du système principal)
        donnees_test = []
        for i in range(1000):
            donnees_test.append({
                'partie_id': f'test_{i}',
                'main': i % 100,
                'ratio_l4': 0.3 + (i % 7) * 0.1,  # Variation entre 0.3 et 0.9
                'ratio_l5': 0.2 + (i % 8) * 0.1,  # Variation entre 0.2 et 0.9
                'diff_l4': (i % 5) * 0.02,
                'diff_l5': (i % 6) * 0.02,
                'pattern': 'S' if i % 3 == 0 else 'O',
                'index3': i % 10
            })

        # Enrichir avec la métrique Shannon
        donnees_enrichies = analyseur_shannon.calculer_metrique_sur_donnees(donnees_test)
        print(f"📊 {len(donnees_enrichies)} données enrichies avec entropie de Shannon")

        # Analyser les conditions
        conditions_s, conditions_o = analyseur_shannon.analyser_toutes_conditions_avec_metrique(donnees_enrichies)

        # Calculer les corrélations
        correlations_stats = analyseur_shannon.calculer_correlations_essentielles_metrique(donnees_enrichies)

        # Générer le rapport
        nom_rapport = analyseur_shannon.generer_tableau_predictif_avec_metrique(len(donnees_enrichies))
        print(f"✅ Rapport généré: {nom_rapport}")

        # Afficher les résultats
        analyseur_shannon.afficher_resultats_avec_metrique()

        return True

    except Exception as e:
        print(f"❌ Erreur durant l'exemple Shannon: {e}")
        return False


# ============================================================================
# POINT D'ENTRÉE PRINCIPAL MODIFIÉ
# ============================================================================

if __name__ == "__main__":
    print("🚀 LANCEMENT ANALYSE COMPLÈTE AVEC DIFF ET MÉTRIQUES GÉNÉRIQUES")
    print("=" * 70)

    # Analyse principale AVEC DIFF (existante)
    print("\n📊 PHASE 1: ANALYSE DIFF ORIGINALE")
    print("-" * 40)
    success_diff = analyser_conditions_predictives_so_avec_diff()

    if success_diff:
        print(f"\n🎯 ANALYSE AVEC DIFF RÉUSSIE !")
        print("✅ Tableau prédictif AVEC DIFF généré")
        print("📊 Variable DIFF (cohérence L4/L5) incluse")
        print("🚀 Conditions de qualité signal identifiées")
    else:
        print(f"\n❌ ANALYSE AVEC DIFF ÉCHOUÉE")
        print("⚠️ Vérifiez les erreurs et corrigez les problèmes")

    # Exemple avec l'analyseur générique
    print("\n📊 PHASE 2: EXEMPLE ANALYSEUR GÉNÉRIQUE (SHANNON)")
    print("-" * 50)
    success_shannon = exemple_analyse_shannon_entropy()

    if success_shannon:
        print(f"\n🎯 EXEMPLE SHANNON RÉUSSI !")
        print("✅ Architecture DIFF reproduite pour Shannon")
        print("📊 Classe générique opérationnelle")
    else:
        print(f"\n❌ EXEMPLE SHANNON ÉCHOUÉ")
        print("⚠️ Vérifiez l'implémentation générique")

    print("\n" + "=" * 70)
    print("🎯 RÉSUMÉ FINAL:")
    print(f"   DIFF Analysis: {'✅ SUCCÈS' if success_diff else '❌ ÉCHEC'}")
    print(f"   Generic Analysis: {'✅ SUCCÈS' if success_shannon else '❌ ÉCHEC'}")
    print("📊 Architecture générique disponible pour toutes les métriques d'entropie")
    print("=" * 70)
